#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简单的MaixCAM字符显示程序
功能: 接收STM32发送的单个字符并在屏幕上显示
适用设备: MaixCAM
开发环境: MaixPy
版权归属: 米醋电子工作室
创建日期: 2024年12月
"""

from maix import uart, app, time, display, image

def main():
    """主函数"""
    print("=" * 40)
    print("MaixCAM简单字符显示程序")
    print("按键1显示1，按键2显示2...")
    print("=" * 40)
    
    # 初始化串口
    device = "/dev/ttyS0"
    baudrate = 115200
    serial = uart.UART(device, baudrate)
    
    # 初始化显示
    disp = display.Display()
    screen_width = disp.width()
    screen_height = disp.height()
    
    print(f"串口: {device}, 波特率: {baudrate}")
    print(f"屏幕尺寸: {screen_width}x{screen_height}")
    print("等待STM32发送字符...")
    
    # 显示初始信息
    img = image.Image(screen_width, screen_height, image.Format.FMT_RGB888)
    img.clear(image.COLOR_BLACK)
    img.draw_string(10, 10, "MaixCAM Ready", image.COLOR_GREEN, font_size=32)
    img.draw_string(10, 50, "Waiting for STM32...", image.COLOR_WHITE, font_size=24)
    disp.show(img)
    
    # 接收到的字符列表
    received_chars = []
    
    try:
        while not app.need_exit():
            # 读取串口数据
            data = serial.read()
            if data:
                # 将字节转换为字符
                try:
                    char_str = data.decode('utf-8', errors='ignore').strip()
                    if char_str:
                        print(f"收到字符: {char_str}")
                        
                        # 添加到字符列表
                        received_chars.append(char_str)
                        
                        # 限制显示字符数量
                        if len(received_chars) > 20:
                            received_chars.pop(0)
                        
                        # 更新显示
                        img = image.Image(screen_width, screen_height, image.Format.FMT_RGB888)
                        img.clear(image.COLOR_BLACK)
                        
                        # 显示标题
                        img.draw_string(10, 10, "STM32 -> MaixCAM", image.COLOR_GREEN, font_size=28)
                        
                        # 显示接收到的字符
                        y_pos = 60
                        for i, char in enumerate(received_chars):
                            # 不同字符用不同颜色
                            if char.isdigit():
                                color = image.COLOR_CYAN
                            elif char.isalpha():
                                color = image.COLOR_YELLOW
                            else:
                                color = image.COLOR_WHITE
                            
                            display_text = f"[{i+1:2d}] {char}"
                            img.draw_string(10, y_pos, display_text, color, font_size=24)
                            y_pos += 30
                            
                            # 防止超出屏幕
                            if y_pos > screen_height - 30:
                                break
                        
                        # 显示最新字符(大字体)
                        if received_chars:
                            latest_char = received_chars[-1]
                            img.draw_string(screen_width - 100, 60, f"'{latest_char}'", 
                                          image.COLOR_RED, font_size=48)
                        
                        # 显示时间
                        current_time = time.strftime("%H:%M:%S")
                        img.draw_string(screen_width - 100, 10, current_time, 
                                      image.COLOR_GRAY, font_size=16)
                        
                        # 更新屏幕
                        disp.show(img)
                        
                except Exception as e:
                    print(f"字符处理错误: {e}")
            
            # 短暂延时
            time.sleep_ms(10)
            
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
    finally:
        print("程序结束")

if __name__ == "__main__":
    main()
