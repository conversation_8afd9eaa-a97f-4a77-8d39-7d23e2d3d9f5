#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MaixCAM字符显示程序 - 适配STM32 Camera_SendByte
功能: 接收STM32通过Camera_SendByte发送的字符并在屏幕显示
适用设备: MaixCAM
开发环境: MaixPy
版权归属: 米醋电子工作室
创建日期: 2024年12月
"""

from maix import uart, app, time, display, image

def main():
    """主函数"""
    print("=" * 50)
    print("MaixCAM字符显示程序")
    print("适配STM32 Camera_SendByte函数")
    print("按键1显示1，按键2显示2...")
    print("=" * 50)
    
    # 初始化串口 - 匹配STM32的Camera_Init配置
    device = "/dev/ttyS0"  # MaixCAM的UART0对应STM32的USART2
    baudrate = 115200      # 匹配STM32的波特率
    serial = uart.UART(device, baudrate)
    
    # 初始化显示
    disp = display.Display()
    screen_width = disp.width()
    screen_height = disp.height()
    
    print(f"串口配置: {device}, 波特率: {baudrate}")
    print(f"屏幕尺寸: {screen_width}x{screen_height}")
    print("等待STM32发送字符...")
    
    # 显示初始信息
    img = image.Image(screen_width, screen_height, image.Format.FMT_RGB888)
    img.clear()
    img.draw_string(10, 10, "MaixCAM Ready", image.COLOR_GREEN, scale=2.0)
    img.draw_string(10, 50, "Waiting STM32...", image.COLOR_WHITE, scale=1.5)
    img.draw_string(10, 90, "Camera_SendByte Mode", image.COLOR_BLUE, scale=1.2)
    disp.show(img)
    
    # 接收数据缓冲区
    received_chars = []
    last_receive_time = time.time()
    
    try:
        while not app.need_exit():
            # 读取串口数据 - 适配Camera_SendByte的单字节发送
            data = serial.read(1)  # 每次读取1字节，匹配Camera_SendByte
            
            if data and len(data) > 0:
                current_time = time.time()
                
                # 处理接收到的字节数据
                try:
                    # Camera_SendByte发送的是单个字节
                    byte_value = data[0]
                    
                    # 将字节转换为字符（如果是可打印字符）
                    if 32 <= byte_value <= 126:  # ASCII可打印字符范围
                        char_str = chr(byte_value)
                        print(f"收到字符: '{char_str}' (0x{byte_value:02X})")
                        
                        # 添加到字符列表
                        received_chars.append({
                            'char': char_str,
                            'hex': f"0x{byte_value:02X}",
                            'time': current_time
                        })
                        
                        # 限制显示字符数量
                        if len(received_chars) > 15:
                            received_chars.pop(0)
                        
                        # 更新显示
                        img = image.Image(screen_width, screen_height, image.Format.FMT_RGB888)
                        img.clear()
                        
                        # 显示标题
                        img.draw_string(10, 10, "STM32 -> MaixCAM", image.COLOR_GREEN, scale=1.8)
                        img.draw_string(10, 40, "Camera_SendByte", image.COLOR_BLUE, scale=1.2)
                        
                        # 显示接收到的字符
                        y_pos = 80
                        for i, char_info in enumerate(received_chars):
                            char = char_info['char']
                            hex_val = char_info['hex']
                            
                            # 不同字符用不同颜色 - 使用可用的颜色常量
                            if char.isdigit():
                                color = image.COLOR_BLUE      # 数字用蓝色
                            elif char.isalpha():
                                color = image.COLOR_YELLOW    # 字母用黄色
                            else:
                                color = image.COLOR_WHITE     # 其他用白色
                            
                            # 显示字符和十六进制值
                            display_text = f"[{i+1:2d}] '{char}' {hex_val}"
                            img.draw_string(10, y_pos, display_text, color, scale=1.3)
                            y_pos += 25
                            
                            # 防止超出屏幕
                            if y_pos > screen_height - 30:
                                break
                        
                        # 右侧显示最新字符(大字体)
                        if received_chars:
                            latest_char = received_chars[-1]['char']
                            img.draw_string(screen_width - 120, 80, f"'{latest_char}'", 
                                          image.COLOR_RED, scale=4.0)
                            
                            # 显示字符的ASCII值
                            ascii_val = ord(latest_char)
                            img.draw_string(screen_width - 120, 140, f"ASCII:{ascii_val}", 
                                          image.COLOR_YELLOW, scale=1.0)
                        
                        # 显示时间和统计信息
                        current_time_str = time.strftime("%H:%M:%S")
                        img.draw_string(screen_width - 120, 10, current_time_str, 
                                      image.COLOR_GRAY, scale=1.0)
                        
                        # 显示接收统计
                        total_chars = len(received_chars)
                        img.draw_string(screen_width - 120, 30, f"Total:{total_chars}", 
                                      image.COLOR_GRAY, scale=1.0)
                        
                        # 更新屏幕
                        disp.show(img)
                        
                        last_receive_time = current_time
                        
                    else:
                        # 非可打印字符，显示十六进制值
                        print(f"收到非可打印字节: 0x{byte_value:02X}")
                        
                except Exception as e:
                    print(f"字符处理错误: {e}")
            
            else:
                # 没有数据时，检查是否需要更新显示
                current_time = time.time()
                if current_time - last_receive_time > 5.0:  # 5秒无数据时显示等待状态
                    img = image.Image(screen_width, screen_height, image.Format.FMT_RGB888)
                    img.clear()
                    img.draw_string(10, 10, "MaixCAM Ready", image.COLOR_GREEN, scale=2.0)
                    img.draw_string(10, 50, "Waiting STM32...", image.COLOR_WHITE, scale=1.5)
                    img.draw_string(10, 90, "Camera_SendByte Mode", image.COLOR_BLUE, scale=1.2)
                    
                    # 显示连接状态
                    status_text = "No data received"
                    img.draw_string(10, 130, status_text, image.COLOR_YELLOW, scale=1.0)
                    
                    # 显示时间
                    current_time_str = time.strftime("%H:%M:%S")
                    img.draw_string(screen_width - 120, 10, current_time_str, 
                                  image.COLOR_GRAY, scale=1.0)
                    
                    disp.show(img)
                    last_receive_time = current_time  # 重置时间避免频繁更新
            
            # 短暂延时，避免CPU占用过高
            time.sleep_ms(10)
            
    except KeyboardInterrupt:
        print("程序被用户中断")
    except Exception as e:
        print(f"程序运行错误: {e}")
    finally:
        print("程序结束")
        # 显示结束信息
        try:
            img = image.Image(screen_width, screen_height, image.Format.FMT_RGB888)
            img.clear()
            img.draw_string(10, 10, "Program Ended", image.COLOR_RED, scale=2.0)
            disp.show(img)
        except:
            pass

if __name__ == "__main__":
    main()