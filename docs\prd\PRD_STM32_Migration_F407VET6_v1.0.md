# STM32F103C8到F407VET6移植项目需求文档 (PRD)

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2024年12月
- **负责人**: Emma (产品经理)
- **项目代号**: STM32_Migration_F407VET6
- **版权归属**: 米醋电子工作室

## 2. 背景与问题陈述

### 2.1 当前状态分析
基于项目文件分析，当前系统基于STM32F103C8微控制器，使用标准外设库(StdPeriph)开发：

**硬件平台**：
- 目标MCU: STM32F103C8 (Cortex-M3, 64KB Flash, 20KB RAM)
- 时钟频率: 72MHz
- 外设使用: USART, GPIO, LED控制, 矩阵键盘

**软件架构**：
- 开发环境: Keil uVision5 + ARM-ADS工具链
- 库类型: STM32F10x标准外设库
- 项目结构: 模块化设计(USER/HARDWARE/SYSTEM/CORE)

### 2.2 移植需求
需要将整个项目移植到STM32F407VET6平台，并升级到HAL库架构。

## 3. 目标与成功指标

### 3.1 项目目标(Objectives)
1. **硬件平台升级**: 从F103C8迁移到F407VET6
2. **软件架构现代化**: 从StdPeriph库升级到HAL库
3. **功能完整性保证**: 确保所有原有功能正常工作
4. **性能优化**: 充分利用F407VET6的高性能特性

### 3.2 关键结果(Key Results)
- [ ] 硬件引脚映射100%完成
- [ ] 所有外设功能正常工作
- [ ] 代码编译无错误无警告
- [ ] 功能测试通过率100%
- [ ] 性能提升≥30%（相比原F103C8）

### 3.3 反向指标(Counter Metrics)
- 代码复杂度不应显著增加
- 内存使用不应超过F407VET6的50%
- 移植时间不应超过预期20%

## 4. 软硬件关系分析

### 4.1 硬件对比分析

| 特性 | STM32F103C8 | STM32F407VET6 | 影响分析 |
|------|-------------|---------------|----------|
| **内核** | Cortex-M3 | Cortex-M4F | 支持FPU，性能提升 |
| **主频** | 72MHz | 168MHz | 2.3倍性能提升 |
| **Flash** | 64KB | 512KB | 8倍存储空间 |
| **RAM** | 20KB | 192KB | 9.6倍内存空间 |
| **封装** | LQFP48 | LQFP100 | 更多IO引脚 |
| **USART** | 3个 | 6个 | 更多串口资源 |
| **GPIO** | 37个 | 82个 | 更丰富的IO |

### 4.2 引脚映射关系

#### 4.2.1 关键外设引脚对应
```
原F103C8引脚 -> F407VET6引脚映射：

USART1:
- TX: PA9  -> PA9  (兼容)
- RX: PA10 -> PA10 (兼容)

LED控制:
- 需要重新定义GPIO引脚
- 建议使用PE8-PE15 (板载LED)

矩阵键盘:
- 原引脚可能需要重新分配
- 利用F407VET6更多的GPIO资源

系统时钟:
- HSE: 8MHz -> 8MHz (兼容)
- PLL配置需要重新计算
```

### 4.3 软件架构对比

#### 4.3.1 库函数对比
| 功能模块 | StdPeriph库 | HAL库 | 迁移复杂度 |
|----------|-------------|-------|------------|
| **GPIO** | GPIO_Init() | HAL_GPIO_Init() | 中等 |
| **USART** | USART_Init() | HAL_UART_Init() | 中等 |
| **RCC** | RCC_Configuration() | HAL_RCC_ClockConfig() | 高 |
| **中断** | NVIC_Configuration() | HAL_NVIC_SetPriority() | 低 |
| **延时** | 自定义delay | HAL_Delay() | 低 |

#### 4.3.2 项目结构对比
```
原项目结构:
USART/
├── USER/           # 用户应用代码
├── HARDWARE/       # 硬件驱动层
├── SYSTEM/         # 系统层(delay, sys, usart)
├── CORE/           # 内核启动文件
└── STM32F10x_FWLib/ # 标准外设库

目标结构:
USART_F407VET6/
├── Core/
│   ├── Inc/        # 头文件
│   ├── Src/        # 源文件
│   └── Startup/    # 启动文件
├── Drivers/
│   ├── STM32F4xx_HAL_Driver/ # HAL库
│   └── CMSIS/      # CMSIS层
├── Middlewares/    # 中间件
└── Application/    # 应用层
    ├── Hardware/   # 硬件抽象层
    └── User/       # 用户代码
```

## 5. 功能规格详述

### 5.1 核心功能迁移清单
1. **USART通信功能**
   - 保持原有通信协议
   - 升级到HAL_UART接口
   - 支持DMA传输(可选优化)

2. **LED控制功能**
   - 重新映射GPIO引脚
   - 保持原有控制逻辑
   - 利用F407VET6板载LED

3. **矩阵键盘功能**
   - 重新分配GPIO引脚
   - 保持扫描算法
   - 优化响应速度

4. **系统时钟配置**
   - 重新设计时钟树
   - 优化到168MHz主频
   - 配置外设时钟

### 5.2 新增功能机会
- **FPU浮点运算**: 利用Cortex-M4F的FPU
- **DMA传输**: 优化数据传输效率
- **更多外设**: 利用F407VET6丰富的外设资源

## 6. 范围定义

### 6.1 包含功能(In Scope)
- [x] 完整的硬件平台迁移
- [x] StdPeriph到HAL库的转换
- [x] 所有现有功能的保持
- [x] 项目结构现代化
- [x] 编译环境配置
- [x] 基础性能优化

### 6.2 排除功能(Out of Scope)
- [ ] 新功能开发(第一阶段)
- [ ] 高级优化特性(如RTOS集成)
- [ ] 复杂的中间件集成
- [ ] GUI界面开发

## 7. 依赖与风险

### 7.1 技术依赖
- STM32CubeMX配置工具
- STM32F4xx HAL库
- 兼容的开发环境(推荐STM32CubeIDE)
- F407VET6开发板

### 7.2 潜在风险
| 风险 | 概率 | 影响 | 缓解措施 |
|------|------|------|----------|
| 引脚冲突 | 中 | 高 | 详细引脚规划 |
| 时钟配置错误 | 中 | 高 | 使用CubeMX生成 |
| HAL库兼容性 | 低 | 中 | 充分测试验证 |
| 性能回归 | 低 | 中 | 性能基准测试 |

## 8. 发布初步计划

### 8.1 开发阶段
1. **Phase 1**: 硬件分析与引脚映射 (1天)
2. **Phase 2**: HAL库项目框架搭建 (1天)
3. **Phase 3**: 核心功能迁移 (2-3天)
4. **Phase 4**: 测试与优化 (1-2天)

### 8.2 验收标准
- 所有原有功能正常工作
- 代码质量符合HAL库规范
- 性能测试通过
- 文档完整更新

---

**文档状态**: ✅ 已完成
**下一步**: 等待Mike指令进入技术架构设计阶段