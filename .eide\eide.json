{"name": "USART", "type": "ARM", "dependenceList": [], "srcDirs": [], "virtualFolder": {"name": "<virtual_root>", "files": [], "folders": [{"name": "USER", "files": [{"path": "main.c"}, {"path": "stm32f10x_it.c"}, {"path": "system_stm32f10x.c"}], "folders": []}, {"name": "HARDWARE", "files": [{"path": "../HARDWARE/LED/led.c"}, {"path": "../HARDWARE/KEY/matrix_keyboard.c"}], "folders": []}, {"name": "SYSTEM", "files": [{"path": "../SYSTEM/delay/delay.c"}, {"path": "../SYSTEM/sys/sys.c"}, {"path": "../SYSTEM/usart/usart.c"}, {"path": "../SYSTEM/usart/Camera.c"}], "folders": []}, {"name": "CORE", "files": [{"path": "../CORE/core_cm3.c"}, {"path": "../CORE/startup_stm32f10x_hd.s"}], "folders": []}, {"name": "FWLib", "files": [{"path": "../STM32F10x_FWLib/src/stm32f10x_gpio.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_rcc.c"}, {"path": "../STM32F10x_FWLib/src/stm32f10x_usart.c"}, {"path": "../STM32F10x_FWLib/src/misc.c"}], "folders": []}, {"name": "README", "files": [{"path": "../README.TXT"}], "folders": []}]}, "outDir": "build", "deviceName": null, "packDir": null, "miscInfo": {"uid": "8cbf8a4a2f59693a26f42b39abd73cbe"}, "targets": {"Target 1": {"excludeList": [], "toolchain": "AC5", "compileConfig": {"cpuType": "Cortex-M3", "archExtensions": "", "floatingPointHardware": "none", "scatterFilePath": "", "useCustomScatterFile": false, "storageLayout": {"RAM": [{"tag": "RAM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "RAM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}, {"tag": "IRAM", "id": 1, "mem": {"startAddr": "0x20000000", "size": "0x5000"}, "isChecked": true, "noInit": false}, {"tag": "IRAM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "noInit": false}], "ROM": [{"tag": "ROM", "id": 1, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "ROM", "id": 3, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}, {"tag": "IROM", "id": 1, "mem": {"startAddr": "0x8000000", "size": "0x10000"}, "isChecked": true, "isStartup": true}, {"tag": "IROM", "id": 2, "mem": {"startAddr": "0x0", "size": "0x0"}, "isChecked": false, "isStartup": false}]}, "options": "null"}, "uploader": "JLink", "uploadConfig": {"bin": "", "baseAddr": "", "cpuInfo": {"vendor": "null", "cpuName": "null"}, "proType": 1, "speed": 8000, "otherCmds": ""}, "uploadConfigMap": {}, "custom_dep": {"name": "default", "incList": ["../HARDWARE/LED", "../SYSTEM/delay", "../SYSTEM/sys", "../SYSTEM/usart", ".", "../STM32F10x_FWLib/inc", "../CORE", ".cmsis/include", "RTE/_Target 1"], "libList": [], "defineList": ["STM32F10X_MD", "USE_STDPERIPH_DRIVER"]}, "builderOptions": {"AC5": {"version": 4, "beforeBuildTasks": [], "afterBuildTasks": [], "global": {"use-microLIB": false, "output-debug-info": "enable"}, "c/cpp-compiler": {"optimization": "level-0", "one-elf-section-per-function": true, "c99-mode": true, "C_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "CXX_FLAGS": "--diag_suppress=1 --diag_suppress=1295", "warnings": "unspecified"}, "asm-compiler": {}, "linker": {"$outputTaskExcludes": [".bin"], "output-format": "elf", "xo-base": "", "ro-base": "0x08000000", "rw-base": "0x20000000"}}}}}, "version": "3.6"}