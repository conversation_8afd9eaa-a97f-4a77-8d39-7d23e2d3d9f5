#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
MaixCAM字符显示接收程序
功能: 接收STM32发送的字符数据并在屏幕上显示
适用设备: MaixCAM
开发环境: MaixPy
版权归属: 米醋电子工作室
创建日期: 2024年12月
"""

from maix import uart, app, time, display, image
import struct

class STM32DisplayReceiver:
    def __init__(self):
        """初始化STM32显示接收器"""
        # 串口配置
        self.device = "/dev/ttyS0"  # 使用UART0
        self.baudrate = 115200
        self.serial = uart.UART(self.device, self.baudrate)
        
        # 显示配置
        self.disp = display.Display()
        self.screen_width = self.disp.width()
        self.screen_height = self.disp.height()
        
        # 文本显示配置
        self.font_size = 24
        self.line_height = 30
        self.max_lines = self.screen_height // self.line_height
        self.text_lines = []  # 存储显示的文本行
        self.current_line = 0
        
        # 颜色映射
        self.color_map = {
            0x00: image.COLOR_WHITE,    # 白色
            0x01: image.COLOR_RED,      # 红色
            0x02: image.COLOR_GREEN,    # 绿色
            0x03: image.COLOR_BLUE,     # 蓝色
            0x04: image.COLOR_YELLOW,   # 黄色
            0x05: image.COLOR_CYAN,     # 青色
            0x06: image.COLOR_MAGENTA,  # 洋红色
        }
        
        # 协议定义
        self.FRAME_HEAD = 0xAA
        self.FRAME_TAIL = 0xFF
        self.CMD_TEXT_DISPLAY = 0x01
        self.CMD_CLEAR_SCREEN = 0x02
        self.CMD_SET_COLOR = 0x03
        
        # 接收缓冲区
        self.rx_buffer = bytearray()
        
        print(f"STM32显示接收器初始化完成")
        print(f"串口: {self.device}, 波特率: {self.baudrate}")
        print(f"屏幕尺寸: {self.screen_width}x{self.screen_height}")
        
        # 设置接收回调函数
        self.serial.set_received_callback(self.on_data_received)
        
        # 显示初始信息
        self.clear_screen()
        self.add_text_line("MaixCAM Ready", image.COLOR_GREEN)
        self.add_text_line("Waiting for STM32...", image.COLOR_WHITE)
        self.update_display()
    
    def on_data_received(self, serial: uart.UART, data: bytes):
        """串口数据接收回调函数"""
        try:
            # 将接收到的数据添加到缓冲区
            self.rx_buffer.extend(data)
            
            # 处理缓冲区中的完整数据帧
            self.process_buffer()
            
        except Exception as e:
            print(f"数据接收处理错误: {e}")
    
    def process_buffer(self):
        """处理接收缓冲区中的数据"""
        while len(self.rx_buffer) >= 6:  # 最小帧长度
            # 查找帧头
            head_index = -1
            for i in range(len(self.rx_buffer)):
                if self.rx_buffer[i] == self.FRAME_HEAD:
                    head_index = i
                    break
            
            if head_index == -1:
                # 没有找到帧头，清空缓冲区
                self.rx_buffer.clear()
                return
            
            # 移除帧头之前的无效数据
            if head_index > 0:
                self.rx_buffer = self.rx_buffer[head_index:]
            
            # 检查是否有足够的数据解析帧头
            if len(self.rx_buffer) < 6:
                return
            
            # 解析帧头信息
            frame_head = self.rx_buffer[0]  # 0xAA
            cmd = self.rx_buffer[1]
            data_len_low = self.rx_buffer[2]
            data_len_high = self.rx_buffer[3]
            data_len = data_len_low | (data_len_high << 8)
            
            # 计算完整帧长度
            frame_len = 6 + data_len  # 帧头(1) + 命令(1) + 长度(2) + 数据(data_len) + 校验(1) + 帧尾(1)
            
            # 检查是否接收到完整帧
            if len(self.rx_buffer) < frame_len:
                return
            
            # 提取完整帧
            frame = self.rx_buffer[:frame_len]
            
            # 验证帧尾
            if frame[-1] != self.FRAME_TAIL:
                # 帧尾不正确，移除第一个字节继续查找
                self.rx_buffer = self.rx_buffer[1:]
                continue
            
            # 提取数据和校验
            frame_data = frame[4:4+data_len] if data_len > 0 else b''
            checksum_received = frame[4+data_len]
            
            # 计算校验和
            checksum_calculated = cmd + data_len_low + data_len_high
            for byte in frame_data:
                checksum_calculated += byte
            checksum_calculated &= 0xFF
            
            # 验证校验和
            if checksum_received != checksum_calculated:
                print(f"校验和错误: 接收={checksum_received:02X}, 计算={checksum_calculated:02X}")
                self.rx_buffer = self.rx_buffer[1:]
                continue
            
            # 处理有效帧
            self.process_frame(cmd, frame_data)
            
            # 移除已处理的帧
            self.rx_buffer = self.rx_buffer[frame_len:]
            
            # 发送确认
            self.send_ack()
    
    def process_frame(self, cmd: int, data: bytes):
        """处理接收到的数据帧"""
        try:
            if cmd == self.CMD_TEXT_DISPLAY:
                # 文本显示命令
                if len(data) >= 1:
                    color_code = data[0]
                    text_data = data[1:].decode('utf-8', errors='ignore')
                    color = self.color_map.get(color_code, image.COLOR_WHITE)
                    
                    print(f"显示文本: {text_data}, 颜色: {color_code}")
                    self.add_text_line(text_data, color)
                    self.update_display()
            
            elif cmd == self.CMD_CLEAR_SCREEN:
                # 清屏命令
                print("清除屏幕")
                self.clear_screen()
                self.update_display()
            
            elif cmd == self.CMD_SET_COLOR:
                # 设置颜色命令
                if len(data) >= 1:
                    color_code = data[0]
                    print(f"设置默认颜色: {color_code}")
            
            else:
                print(f"未知命令: {cmd:02X}")
                
        except Exception as e:
            print(f"帧处理错误: {e}")
    
    def add_text_line(self, text: str, color):
        """添加文本行"""
        # 添加新文本行
        self.text_lines.append({
            'text': text,
            'color': color,
            'timestamp': time.time()
        })
        
        # 限制显示行数
        if len(self.text_lines) > self.max_lines:
            self.text_lines.pop(0)
    
    def clear_screen(self):
        """清除屏幕"""
        self.text_lines.clear()
    
    def update_display(self):
        """更新显示"""
        try:
            # 创建图像
            img = image.Image(self.screen_width, self.screen_height, image.Format.FMT_RGB888)
            img.clear(image.COLOR_BLACK)
            
            # 绘制文本行
            y_pos = 10
            for line_info in self.text_lines:
                text = line_info['text']
                color = line_info['color']
                
                # 绘制文本
                img.draw_string(10, y_pos, text, color, font_size=self.font_size)
                y_pos += self.line_height
                
                # 防止超出屏幕
                if y_pos > self.screen_height - self.line_height:
                    break
            
            # 在右上角显示时间戳
            current_time = time.strftime("%H:%M:%S")
            img.draw_string(self.screen_width - 100, 10, current_time, 
                          image.COLOR_GRAY, font_size=16)
            
            # 显示图像
            self.disp.show(img)
            
        except Exception as e:
            print(f"显示更新错误: {e}")
    
    def send_ack(self):
        """发送确认信号"""
        try:
            self.serial.write(b'\x55')  # 发送确认码
        except Exception as e:
            print(f"发送确认错误: {e}")
    
    def run(self):
        """运行主循环"""
        print("开始接收STM32数据...")
        
        try:
            while not app.need_exit():
                # 定期更新显示(即使没有新数据)
                self.update_display()
                time.sleep_ms(100)  # 100ms更新一次显示
                
        except KeyboardInterrupt:
            print("程序被用户中断")
        except Exception as e:
            print(f"运行错误: {e}")
        finally:
            print("程序结束")

def main():
    """主函数"""
    print("=" * 50)
    print("MaixCAM STM32字符显示程序")
    print("版权归属: 米醋电子工作室")
    print("=" * 50)
    
    # 创建并运行显示接收器
    receiver = STM32DisplayReceiver()
    receiver.run()

if __name__ == "__main__":
    main()
