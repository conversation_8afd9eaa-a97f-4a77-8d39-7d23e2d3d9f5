# 4×4矩阵键盘使用指南

## 1. 硬件连接

### 1.1 引脚连接表
| 功能 | STM32引脚 | 键盘连接 | 物理引脚号 |
|------|-----------|----------|-----------|
| **列线(输出)** | | | |
| C1 | PB12 | 键盘列1 | 25 |
| C2 | PB13 | 键盘列2 | 26 |
| C3 | PB14 | 键盘列3 | 27 |
| C4 | PB15 | 键盘列4 | 28 |
| **行线(输入)** | | | |
| R1 | PB8 | 键盘行1 | 21 |
| R2 | PB9 | 键盘行2 | 22 |
| R3 | PC14 | 键盘行3 | 3 |
| R4 | PC15 | 键盘行4 | 4 |

### 1.2 键盘布局
```
    C1   C2   C3   C4
R1  [1]  [2]  [3]  [A]
R2  [4]  [5]  [6]  [B]
R3  [7]  [8]  [9]  [C]
R4  [*]  [0]  [#]  [D]
```

## 2. 软件使用

### 2.1 头文件包含
```c
#include "matrix_keyboard_4x4.h"
```

### 2.2 初始化
```c
int main(void)
{
    // 系统初始化
    delay_init();
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(9600);
    
    // 矩阵键盘初始化
    Matrix_Keyboard_Init();
    
    while(1)
    {
        // 主循环代码
    }
}
```

### 2.3 基本使用示例
```c
void keyboard_demo(void)
{
    uint8_t key_value;
    char key_char;
    
    while(1)
    {
        key_value = Matrix_Keyboard_GetKey();
        if(key_value != KEY_VALUE_NONE)
        {
            key_char = Matrix_Keyboard_GetChar(key_value);
            printf("按键: %c (值: 0x%02X)\r\n", key_char, key_value);
        }
        delay_ms(10);  // 10ms扫描间隔
    }
}
```

### 2.4 高级使用示例
```c
void keyboard_advanced_demo(void)
{
    uint8_t key_value;
    uint8_t key_state;
    char input_buffer[16] = {0};
    uint8_t buffer_index = 0;
    
    printf("请输入密码(按#确认):\r\n");
    
    while(1)
    {
        key_value = Matrix_Keyboard_GetKey();
        key_state = Matrix_Keyboard_GetKeyState();
        
        if(key_value != KEY_VALUE_NONE)
        {
            char key_char = Matrix_Keyboard_GetChar(key_value);
            
            if(key_char == '#')  // 确认键
            {
                printf("\r\n输入完成: %s\r\n", input_buffer);
                buffer_index = 0;
                memset(input_buffer, 0, sizeof(input_buffer));
            }
            else if(key_char == '*')  // 清除键
            {
                if(buffer_index > 0)
                {
                    buffer_index--;
                    input_buffer[buffer_index] = '\0';
                    printf("\b \b");  // 退格显示
                }
            }
            else  // 普通按键
            {
                if(buffer_index < 15)
                {
                    input_buffer[buffer_index] = key_char;
                    buffer_index++;
                    printf("%c", key_char);
                }
            }
        }
        
        delay_ms(10);
    }
}
```

## 3. API函数说明

### 3.1 初始化函数
```c
void Matrix_Keyboard_Init(void);
```
- **功能**: 初始化矩阵键盘GPIO
- **参数**: 无
- **返回**: 无
- **说明**: 配置列线为推挽输出，行线为上拉输入

### 3.2 扫描函数
```c
uint8_t Matrix_Keyboard_Scan(void);
```
- **功能**: 扫描矩阵键盘，获取当前按键状态
- **参数**: 无
- **返回**: 按键值(0x00-0x0F有效，0xFF无按键)
- **说明**: 无消抖处理，适合高频调用

### 3.3 获取按键函数
```c
uint8_t Matrix_Keyboard_GetKey(void);
```
- **功能**: 获取按键值(带消抖处理)
- **参数**: 无
- **返回**: 按键值(0x00-0x0F有效，0xFF无按键)
- **说明**: 推荐使用，自动消抖

### 3.4 状态查询函数
```c
uint8_t Matrix_Keyboard_GetKeyState(void);
```
- **功能**: 获取当前按键状态
- **参数**: 无
- **返回**: 0-空闲，1-按下，2-释放
- **说明**: 用于状态机处理

### 3.5 字符转换函数
```c
char Matrix_Keyboard_GetChar(uint8_t key_value);
```
- **功能**: 将按键值转换为字符
- **参数**: key_value - 按键值
- **返回**: 对应字符('0'-'9','A'-'D','*','#')
- **说明**: 便于显示和处理

### 3.6 测试函数
```c
void Matrix_Keyboard_Test(void);
```
- **功能**: 键盘测试程序
- **参数**: 无
- **返回**: 无
- **说明**: 用于验证键盘功能

## 4. 键值对照表

| 按键 | 键值(HEX) | 字符 | 说明 |
|------|-----------|------|------|
| 0 | 0x00 | '0' | 数字0 |
| 1 | 0x01 | '1' | 数字1 |
| 2 | 0x02 | '2' | 数字2 |
| 3 | 0x03 | '3' | 数字3 |
| 4 | 0x04 | '4' | 数字4 |
| 5 | 0x05 | '5' | 数字5 |
| 6 | 0x06 | '6' | 数字6 |
| 7 | 0x07 | '7' | 数字7 |
| 8 | 0x08 | '8' | 数字8 |
| 9 | 0x09 | '9' | 数字9 |
| A | 0x0A | 'A' | 字母A |
| B | 0x0B | 'B' | 字母B |
| C | 0x0C | 'C' | 字母C |
| D | 0x0D | 'D' | 字母D |
| * | 0x0E | '*' | 星号 |
| # | 0x0F | '#' | 井号 |
| 无 | 0xFF | '\0' | 无按键 |

## 5. 注意事项

### 5.1 硬件注意事项
1. **引脚冲突**: 已避开PA2,PA3,PA9,PA10和SPI1引脚
2. **上拉电阻**: 使用内部上拉，无需外接电阻
3. **按键类型**: 支持常开型按键矩阵
4. **电平逻辑**: 低电平有效(按下时为低电平)

### 5.2 软件注意事项
1. **扫描频率**: 建议10ms扫描一次，平衡响应速度和CPU占用
2. **消抖处理**: GetKey函数已内置消抖，无需额外处理
3. **状态机**: 支持按键状态检测，适合复杂应用
4. **中断使用**: 当前为轮询方式，可扩展为中断方式

### 5.3 性能优化
1. **批量操作**: 列线使用连续引脚，便于批量控制
2. **时钟统一**: PB组引脚共用时钟，减少配置
3. **延时优化**: 使用微秒级延时确保电平稳定
4. **内存优化**: 使用静态变量减少栈开销

## 6. 故障排除

### 6.1 常见问题
1. **无响应**: 检查引脚连接和初始化
2. **误触发**: 调整消抖参数或检查硬件干扰
3. **按键粘连**: 检查按键机械结构
4. **部分按键失效**: 检查对应引脚连接

### 6.2 调试方法
1. 使用Matrix_Keyboard_Test()函数测试
2. 用示波器检查引脚电平变化
3. 添加调试输出观察扫描过程
4. 检查GPIO配置是否正确
