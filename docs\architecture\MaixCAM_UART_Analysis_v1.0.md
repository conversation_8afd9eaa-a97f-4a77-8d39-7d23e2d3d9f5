# MaixCAM UART通信深度分析报告

## 1. MaixCAM UART基础架构

### 1.1 硬件接口配置
| 接口 | 设备节点 | 引脚 | 功能 | 备注 |
|------|----------|------|------|------|
| UART0 | /dev/ttyS0 | A16(TX), A17(RX) | 主串口 | USB Type-C转接板引出 |
| UART1 | /dev/ttyS1 | A18(TX), A19(RX) | 扩展串口 | 需手动映射 |

### 1.2 引脚映射机制
```python
from maix import pinmap
# UART1需要手动映射
pinmap.set_pin_function("A18", "UART1_RX")
pinmap.set_pin_function("A19", "UART1_TX")
```

### 1.3 重要硬件特性
- **TX引脚限制**: 开机时TX引脚不能被拉低，否则无法开机
- **Type-C方向性**: 正插反插会导致RX/TX交换
- **开机日志**: UART0会输出开机日志，启动完成后显示"serial ready"

## 2. MaixPy UART API深度解析

### 2.1 基础初始化
```python
from maix import uart

# 基础初始化
device = "/dev/ttyS0"
serial = uart.UART(device, 115200)

# 列出可用串口
ports = uart.list_devices()
```

### 2.2 数据发送机制

#### 2.2.1 字符串发送
```python
# 方法1: 直接发送字符串
serial.write_str("hello world")

# 方法2: 编码后发送
serial.write("hello world".encode())
```

#### 2.2.2 二进制数据发送
```python
from struct import pack

# 发送原始字节
bytes_content = b'\x01\x02\x03'
serial.write(bytes_content)

# 发送结构化数据
num = 10
bytes_content = b'\xAA\xBB\xCC\xDD'  # 帧头
bytes_content += pack("<i", num)      # 小端编码整数
bytes_content += b'\xFF'              # 帧尾
serial.write(bytes_content)
```

### 2.3 数据接收机制详解

#### 2.3.1 read()函数参数组合
| 参数组合 | 行为描述 | 使用场景 |
|----------|----------|----------|
| `read()` | 立即返回缓冲区数据 | 轮询接收 |
| `read(-1, -1)` | 阻塞等待完整数据包 | 同步通信 |
| `read(10, 1000)` | 最多读10字节，超时1秒 | 定长数据包 |

#### 2.3.2 接收示例代码
```python
# 轮询接收
while not app.need_exit():
    data = serial.read()
    if data:
        print("received:", data)
    time.sleep_ms(1)

# 阻塞接收
data = serial.read(len=-1, timeout=-1)
print("complete packet:", data)
```

## 3. 中断回调函数机制 (核心特性)

### 3.1 回调函数设置
```python
def on_received(serial: uart.UART, data: bytes):
    """
    串口接收回调函数
    参数:
        serial: UART对象实例
        data: 接收到的字节数据
    注意: 在独立线程中执行，支持复杂处理
    """
    print("received:", data)
    # 可以进行复杂的数据处理
    process_received_data(data)
    # 可以回发数据
    serial.write(data)

# 设置回调函数
serial.set_received_callback(on_received)
```

### 3.2 回调函数特性
- **异步执行**: 在独立线程中运行，不阻塞主程序
- **实时响应**: 数据到达立即触发，无需轮询
- **复杂处理**: 支持在回调中进行复杂的业务逻辑处理
- **互斥性**: 使用回调时不能同时使用read()函数

### 3.3 完整回调示例
```python
from maix import uart, app, time

def on_received(serial: uart.UART, data: bytes):
    """处理STM32发送的矩阵键盘数据"""
    try:
        # 解析数据
        if len(data) >= 1:
            key_value = data[0]
            print(f"收到按键: 0x{key_value:02X}")
            
            # 根据按键执行不同任务
            if key_value == 0x01:      # 按键'1'
                execute_task_1()
            elif key_value == 0x02:    # 按键'2'
                execute_task_2()
            elif key_value == 0x0A:    # 按键'A'
                execute_task_A()
            
            # 发送确认信息
            serial.write(b'\xAA')  # 确认收到
            
    except Exception as e:
        print(f"处理数据出错: {e}")

def execute_task_1():
    """执行任务1 - 例如启动人脸检测"""
    print("启动人脸检测任务")

def execute_task_2():
    """执行任务2 - 例如启动物体检测"""
    print("启动物体检测任务")

def execute_task_A():
    """执行任务A - 例如拍照保存"""
    print("执行拍照任务")

# 主程序
device = "/dev/ttyS0"
serial = uart.UART(device, 115200)
serial.set_received_callback(on_received)

print("等待STM32矩阵键盘数据...")
while not app.need_exit():
    time.sleep_ms(100)  # 释放CPU资源
```

## 4. STM32与MaixCAM通信协议设计

### 4.1 推荐通信协议

#### 4.1.1 简单键值协议
```
格式: [帧头][键值][校验][帧尾]
示例: 0xAA 0x01 0xAB 0xFF
说明: 
- 帧头: 0xAA (固定)
- 键值: 0x01 (按键'1')
- 校验: 0xAB (帧头+键值)
- 帧尾: 0xFF (固定)
```

#### 4.1.2 STM32发送代码示例
```c
void Send_Key_To_MaixCAM(uint8_t key_value)
{
    uint8_t frame[4];
    frame[0] = 0xAA;              // 帧头
    frame[1] = key_value;         // 键值
    frame[2] = 0xAA + key_value;  // 校验
    frame[3] = 0xFF;              // 帧尾
    
    // 通过USART1发送到MaixCAM
    for(int i = 0; i < 4; i++) {
        while(!(USART1->SR & USART_SR_TXE));
        USART1->DR = frame[i];
    }
}

// 在矩阵键盘检测到按键后调用
uint8_t key = Matrix_Keyboard_GetKey();
if(key != KEY_VALUE_NONE) {
    Send_Key_To_MaixCAM(key);
    printf("发送按键到MaixCAM: 0x%02X\r\n", key);
}
```

### 4.2 MaixCAM接收处理
```python
def parse_stm32_frame(data: bytes) -> int:
    """解析STM32发送的数据帧"""
    if len(data) >= 4:
        if data[0] == 0xAA and data[3] == 0xFF:  # 检查帧头帧尾
            key_value = data[1]
            checksum = data[2]
            if checksum == (0xAA + key_value) & 0xFF:  # 校验
                return key_value
    return -1  # 无效数据

def on_stm32_data(serial: uart.UART, data: bytes):
    """处理STM32数据的回调函数"""
    key_value = parse_stm32_frame(data)
    if key_value >= 0:
        print(f"有效按键: 0x{key_value:02X}")
        execute_key_task(key_value)
        # 发送确认
        serial.write(b'\x55')  # 确认码
    else:
        print("无效数据帧")

def execute_key_task(key_value: int):
    """根据按键值执行对应任务"""
    task_map = {
        0x01: "人脸检测",      # 按键'1'
        0x02: "物体检测",      # 按键'2'
        0x03: "二维码识别",    # 按键'3'
        0x0A: "拍照保存",      # 按键'A'
        0x0B: "录像开始",      # 按键'B'
        0x0C: "录像停止",      # 按键'C'
        0x0D: "系统重启",      # 按键'D'
        0x0E: "清除缓存",      # 按键'*'
        0x0F: "退出程序",      # 按键'#'
    }
    
    task_name = task_map.get(key_value, "未知任务")
    print(f"执行任务: {task_name}")
    
    # 这里可以调用具体的功能函数
    if key_value == 0x01:
        start_face_detection()
    elif key_value == 0x02:
        start_object_detection()
    # ... 其他任务
```

## 5. 实际应用架构设计

### 5.1 系统架构图
```
STM32F103C8T6                    MaixCAM
┌─────────────────┐             ┌──────────────────┐
│  4×4矩阵键盘    │             │                  │
│  ┌─┬─┬─┬─┐      │   UART1     │   UART0          │
│  │1│2│3│A│      │◄──────────► │   (回调函数)     │
│  ├─┼─┼─┼─┤      │  115200bps  │                  │
│  │4│5│6│B│      │             │   任务调度器     │
│  ├─┼─┼─┼─┤      │             │   ┌────────────┐ │
│  │7│8│9│C│      │             │   │人脸检测    │ │
│  ├─┼─┼─┼─┤      │             │   │物体检测    │ │
│  │*│0│#│D│      │             │   │二维码识别  │ │
│  └─┴─┴─┴─┘      │             │   │拍照录像    │ │
│                 │             │   └────────────┘ │
└─────────────────┘             └──────────────────┘
```

### 5.2 完整集成代码框架

#### 5.2.1 STM32端集成
```c
// main.c中的主循环
int main(void)
{
    // 系统初始化
    delay_init();
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(9600);        // 调试串口
    Camera_Init();          // 摄像头串口(如果需要)
    
    // 矩阵键盘初始化
    Matrix_Keyboard_Init();
    
    // MaixCAM通信串口初始化(使用USART1)
    MaixCAM_UART_Init();
    
    printf("STM32系统启动完成\r\n");
    
    while(1)
    {
        // 扫描矩阵键盘
        uint8_t key = Matrix_Keyboard_GetKey();
        if(key != KEY_VALUE_NONE)
        {
            char key_char = Matrix_Keyboard_GetChar(key);
            printf("按键: %c, 发送到MaixCAM\r\n", key_char);
            
            // 发送到MaixCAM
            Send_Key_To_MaixCAM(key);
            
            // 等待MaixCAM确认
            if(Wait_MaixCAM_ACK(1000)) {  // 1秒超时
                printf("MaixCAM确认收到\r\n");
            } else {
                printf("MaixCAM无响应\r\n");
            }
        }
        
        delay_ms(10);  // 10ms扫描间隔
    }
}
```

#### 5.2.2 MaixCAM端完整应用
```python
from maix import uart, app, time, camera, display
import threading

class KeyboardTaskManager:
    def __init__(self):
        self.serial = uart.UART("/dev/ttyS0", 115200)
        self.serial.set_received_callback(self.on_key_received)
        self.current_task = None
        self.task_running = False
        
    def on_key_received(self, serial: uart.UART, data: bytes):
        """按键接收回调函数"""
        key_value = self.parse_frame(data)
        if key_value >= 0:
            print(f"收到按键: 0x{key_value:02X}")
            self.execute_task(key_value)
            # 发送确认
            serial.write(b'\x55')
    
    def parse_frame(self, data: bytes) -> int:
        """解析数据帧"""
        if len(data) >= 4 and data[0] == 0xAA and data[3] == 0xFF:
            key_value = data[1]
            checksum = data[2]
            if checksum == (0xAA + key_value) & 0xFF:
                return key_value
        return -1
    
    def execute_task(self, key_value: int):
        """执行对应任务"""
        # 停止当前任务
        self.stop_current_task()
        
        # 启动新任务
        if key_value == 0x01:      # 按键'1'
            self.start_face_detection()
        elif key_value == 0x02:    # 按键'2'
            self.start_object_detection()
        elif key_value == 0x03:    # 按键'3'
            self.start_qr_detection()
        elif key_value == 0x0A:    # 按键'A'
            self.take_photo()
        elif key_value == 0x0F:    # 按键'#'
            self.stop_current_task()
    
    def start_face_detection(self):
        """启动人脸检测任务"""
        print("启动人脸检测")
        self.current_task = "face_detection"
        self.task_running = True
        threading.Thread(target=self._face_detection_worker).start()
    
    def _face_detection_worker(self):
        """人脸检测工作线程"""
        cam = camera.Camera(512, 512)
        disp = display.Display()
        
        while self.task_running and self.current_task == "face_detection":
            img = cam.read()
            # 这里添加人脸检测代码
            disp.show(img)
            time.sleep_ms(30)
    
    def stop_current_task(self):
        """停止当前任务"""
        self.task_running = False
        self.current_task = None
        print("任务已停止")

# 主程序
if __name__ == "__main__":
    manager = KeyboardTaskManager()
    print("MaixCAM键盘任务管理器启动")
    print("等待STM32按键指令...")
    
    while not app.need_exit():
        time.sleep_ms(100)
```

## 6. 调试和优化建议

### 6.1 调试技巧
1. **串口监控**: 使用示波器或逻辑分析仪监控通信
2. **日志输出**: 在回调函数中添加详细日志
3. **数据验证**: 实现完整的数据校验机制
4. **超时处理**: 设置合理的通信超时时间

### 6.2 性能优化
1. **缓冲区管理**: 合理设置串口缓冲区大小
2. **线程优化**: 避免回调函数中的阻塞操作
3. **内存管理**: 及时释放不用的资源
4. **错误恢复**: 实现通信错误的自动恢复机制

### 6.3 可靠性保证
1. **重传机制**: 实现数据重传确保可靠传输
2. **心跳检测**: 定期发送心跳包检测连接状态
3. **异常处理**: 完善的异常捕获和处理机制
4. **状态同步**: 确保两端状态一致性
