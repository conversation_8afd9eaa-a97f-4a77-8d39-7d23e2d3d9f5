# STM32F103C8到F407VET6详细引脚映射分析文档

## 1. 文档信息
- **版本**: v1.0
- **创建日期**: 2024年12月
- **负责人**: <PERSON> (产品经理) + <PERSON> (架构师)
- **项目代号**: STM32_Pin_Mapping_Analysis
- **版权归属**: 米醋电子工作室

## 2. 原项目引脚使用分析

### 2.1 STM32F103C8引脚配置 (LQFP48封装)

#### 2.1.1 电源引脚
| 引脚号 | 引脚名 | 功能 | 连接 |
|--------|--------|------|------|
| 1 | VBAT | 备份电源 | 3.3V |
| 8 | VSSA | 模拟地 | GND |
| 9 | VDDA | 模拟电源 | 3.3V |
| 24 | VDD_1 | 数字电源1 | 3.3V |
| 36 | VDD_2 | 数字电源2 | 3.3V |
| 48 | VDD_3 | 数字电源3 | 3.3V |
| 12 | VSS_1 | 数字地1 | GND |
| 35 | VSS_2 | 数字地2 | GND |
| 47 | VSS_3 | 数字地3 | GND |

#### 2.1.2 时钟引脚
| 引脚号 | 引脚名 | 功能 | 连接 |
|--------|--------|------|------|
| 5 | OSC_IN | 外部晶振输入 | 8MHz晶振 |
| 6 | OSC_OUT | 外部晶振输出 | 8MHz晶振 |
| 3 | OSC32_IN | 32.768kHz晶振输入 | 32.768kHz晶振 |
| 4 | OSC32_OUT | 32.768kHz晶振输出 | 32.768kHz晶振 |

#### 2.1.3 系统引脚
| 引脚号 | 引脚名 | 功能 | 连接 |
|--------|--------|------|------|
| 7 | NRST | 复位引脚 | 复位电路 |
| 44 | BOOT0 | 启动模式选择 | 下拉电阻 |

#### 2.1.4 USART1引脚 (主要通信接口)
| 引脚号 | 引脚名 | GPIO | AF功能 | 连接用途 |
|--------|--------|------|--------|----------|
| 30 | PA9 | GPIOA_9 | USART1_TX | 串口发送 |
| 31 | PA10 | GPIOA_10 | USART1_RX | 串口接收 |

#### 2.1.5 LED控制引脚 (基于led.c分析)
| 引脚号 | 引脚名 | GPIO | 功能 | 连接 |
|--------|--------|------|------|------|
| 待分析 | PC13 | GPIOC_13 | LED控制 | 板载LED |
| 待分析 | PA5 | GPIOA_5 | LED控制 | 外接LED |

#### 2.1.6 矩阵键盘引脚 (基于matrix_keyboard.c分析)
| 功能 | 引脚组 | GPIO引脚 | 连接 |
|------|--------|----------|------|
| 行扫描 | ROW1-4 | PB0-PB3 | 键盘行线 |
| 列检测 | COL1-4 | PB4-PB7 | 键盘列线 |

#### 2.1.7 调试接口
| 引脚号 | 引脚名 | 功能 | 连接 |
|--------|--------|------|------|
| 34 | PA13 | SWDIO | SWD调试数据 |
| 37 | PA14 | SWCLK | SWD调试时钟 |

## 3. STM32F407VET6引脚配置 (LQFP100封装)

### 3.1 电源引脚分布
| 引脚号 | 引脚名 | 功能 | 连接 |
|--------|--------|------|------|
| 1 | PE2 | GPIO | - |
| 6 | VBAT | 备份电源 | 3.3V |
| 11 | VSS_4 | 数字地 | GND |
| 12 | VDD_4 | 数字电源 | 3.3V |
| 19 | VSS_A | 模拟地 | GND |
| 20 | VDD_A | 模拟电源 | 3.3V |
| 28 | VSS_3 | 数字地 | GND |
| 50 | VDD_3 | 数字电源 | 3.3V |
| 75 | VSS_2 | 数字地 | GND |
| 100 | VDD_2 | 数字电源 | 3.3V |
| 25 | VSS_1 | 数字地 | GND |
| 26 | VDD_1 | 数字电源 | 3.3V |

### 3.2 时钟引脚
| 引脚号 | 引脚名 | 功能 | 连接 |
|--------|--------|------|------|
| 23 | OSC_IN | 外部晶振输入 | 8MHz晶振 |
| 24 | OSC_OUT | 外部晶振输出 | 8MHz晶振 |
| 5 | OSC32_IN | 32.768kHz晶振输入 | 32.768kHz晶振 |
| 6 | OSC32_OUT | 32.768kHz晶振输出 | 32.768kHz晶振 |

### 3.3 系统引脚
| 引脚号 | 引脚名 | 功能 | 连接 |
|--------|--------|------|------|
| 14 | NRST | 复位引脚 | 复位电路 |
| 94 | BOOT0 | 启动模式选择 | 下拉电阻 |

## 4. 详细引脚映射方案

### 4.1 USART1映射 (完全兼容)
| 功能 | F103C8 | F407VET6 | 兼容性 | 备注 |
|------|--------|----------|--------|------|
| USART1_TX | PA9 (引脚30) | PA9 (引脚68) | ✅ 完全兼容 | 无需修改 |
| USART1_RX | PA10 (引脚31) | PA10 (引脚69) | ✅ 完全兼容 | 无需修改 |

### 4.2 LED控制映射
| 功能 | F103C8 | F407VET6推荐 | 优势 | 配置 |
|------|--------|---------------|------|------|
| LED1 | PC13 | PE8 (引脚59) | 板载LED | 推挽输出 |
| LED2 | PA5 | PE9 (引脚60) | 板载LED | 推挽输出 |
| LED3 | - | PE10 (引脚61) | 额外LED | 推挽输出 |
| LED4 | - | PE11 (引脚62) | 额外LED | 推挽输出 |

### 4.3 矩阵键盘映射 (优化方案)
| 功能 | F103C8 | F407VET6推荐 | 引脚号 | 配置 |
|------|--------|---------------|--------|------|
| ROW1 | PB0 | PD8 | 55 | 推挽输出 |
| ROW2 | PB1 | PD9 | 56 | 推挽输出 |
| ROW3 | PB2 | PD10 | 57 | 推挽输出 |
| ROW4 | PB3 | PD11 | 58 | 推挽输出 |
| COL1 | PB4 | PD12 | 81 | 上拉输入 |
| COL2 | PB5 | PD13 | 82 | 上拉输入 |
| COL3 | PB6 | PD14 | 85 | 上拉输入 |
| COL4 | PB7 | PD15 | 86 | 上拉输入 |

### 4.4 调试接口映射
| 功能 | F103C8 | F407VET6 | 兼容性 |
|------|--------|----------|--------|
| SWDIO | PA13 (引脚34) | PA13 (引脚72) | ✅ 完全兼容 |
| SWCLK | PA14 (引脚37) | PA14 (引脚76) | ✅ 完全兼容 |

### 4.5 额外可用资源 (F407VET6独有)
| 外设 | 引脚 | 功能 | 建议用途 |
|------|------|------|----------|
| USART2 | PA2/PA3 | 串口2 | 扩展通信 |
| USART3 | PB10/PB11 | 串口3 | 调试输出 |
| SPI1 | PA5/PA6/PA7 | SPI接口 | 高速数据 |
| I2C1 | PB6/PB7 | I2C接口 | 传感器通信 |
| ADC1 | PA0-PA7 | 模数转换 | 模拟信号采集 |
| DAC | PA4/PA5 | 数模转换 | 模拟信号输出 |

## 5. GPIO配置对比

### 5.1 StdPeriph vs HAL库配置对比

#### 5.1.1 LED GPIO配置
**原F103C8 StdPeriph配置:**
```c
GPIO_InitTypeDef GPIO_InitStructure;
RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOC, ENABLE);
GPIO_InitStructure.GPIO_Pin = GPIO_Pin_13;
GPIO_InitStructure.GPIO_Mode = GPIO_Mode_Out_PP;
GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
GPIO_Init(GPIOC, &GPIO_InitStructure);
```

**目标F407VET6 HAL配置:**
```c
GPIO_InitTypeDef GPIO_InitStruct = {0};
__HAL_RCC_GPIOE_CLK_ENABLE();
GPIO_InitStruct.Pin = GPIO_PIN_8;
GPIO_InitStruct.Mode = GPIO_MODE_OUTPUT_PP;
GPIO_InitStruct.Pull = GPIO_NOPULL;
GPIO_InitStruct.Speed = GPIO_SPEED_FREQ_LOW;
HAL_GPIO_Init(GPIOE, &GPIO_InitStruct);
```

#### 5.1.2 USART配置对比
**原F103C8 StdPeriph配置:**
```c
USART_InitTypeDef USART_InitStructure;
RCC_APB2PeriphClockCmd(RCC_APB2Periph_USART1, ENABLE);
USART_InitStructure.USART_BaudRate = 115200;
USART_InitStructure.USART_WordLength = USART_WordLength_8b;
USART_InitStructure.USART_StopBits = USART_StopBits_1;
USART_InitStructure.USART_Parity = USART_Parity_No;
USART_Init(USART1, &USART_InitStructure);
```

**目标F407VET6 HAL配置:**
```c
UART_HandleTypeDef huart1;
huart1.Instance = USART1;
huart1.Init.BaudRate = 115200;
huart1.Init.WordLength = UART_WORDLENGTH_8B;
huart1.Init.StopBits = UART_STOPBITS_1;
huart1.Init.Parity = UART_PARITY_NONE;
huart1.Init.Mode = UART_MODE_TX_RX;
HAL_UART_Init(&huart1);
```

## 6. 时钟配置分析

### 6.1 F103C8时钟配置
- **HSE**: 8MHz外部晶振
- **PLL**: HSE × 9 = 72MHz
- **HCLK**: 72MHz (系统时钟)
- **PCLK1**: 36MHz (APB1时钟)
- **PCLK2**: 72MHz (APB2时钟)

### 6.2 F407VET6时钟配置
- **HSE**: 8MHz外部晶振
- **PLL**: HSE ÷ 4 × 168 ÷ 2 = 168MHz
- **HCLK**: 168MHz (系统时钟)
- **PCLK1**: 42MHz (APB1时钟)
- **PCLK2**: 84MHz (APB2时钟)

## 7. 迁移检查清单

### 7.1 硬件检查
- [ ] 确认F407VET6开发板引脚连接
- [ ] 验证电源供电(3.3V)
- [ ] 检查晶振连接(8MHz + 32.768kHz)
- [ ] 确认调试接口连接

### 7.2 软件检查
- [ ] 更新GPIO引脚定义
- [ ] 修改时钟配置代码
- [ ] 转换StdPeriph到HAL库调用
- [ ] 更新中断向量表
- [ ] 验证编译配置

### 7.3 功能验证
- [ ] USART通信测试
- [ ] LED控制测试
- [ ] 矩阵键盘扫描测试
- [ ] 系统稳定性测试

---

**文档状态**: ✅ 已完成详细引脚分析
**下一步**: 等待确认后进入代码迁移阶段