# STM32F103C8T6引脚分配方案

## 1. 引脚分配概述

### 1.1 设计约束
- **排除引脚**: PA2, PA3, PA9, PA10 (已被USART占用)
- **预留接口**: SPI1接口 (PA5, PA6, PA7)
- **目标芯片**: STM32F103C8T6 (LQFP48封装)

### 1.2 SPI1接口预留
| 功能 | 引脚 | GPIO | 说明 |
|------|------|------|------|
| SPI1_SCK | PA5 | GPIOA_5 | SPI时钟 |
| SPI1_MISO | PA6 | GPIOA_6 | 主入从出 |
| SPI1_MOSI | PA7 | GPIOA_7 | 主出从入 |
| SPI1_NSS | PA4 | GPIOA_4 | 片选信号(可选) |

## 2. 4×4矩阵键盘引脚分配

### 2.1 最优引脚分配方案

#### 2.1.1 列线(Column) - 输出扫描
| 列号 | 引脚 | GPIO | 物理引脚号 |
|------|------|------|-----------|
| C1 | PB12 | GPIOB_12 | 25 |
| C2 | PB13 | GPIOB_13 | 26 |
| C3 | PB14 | GPIOB_14 | 27 |
| C4 | PB15 | GPIOB_15 | 28 |

#### 2.1.2 行线(Row) - 输入检测
| 行号 | 引脚 | GPIO | 物理引脚号 |
|------|------|------|-----------|
| R1 | PB8 | GPIOB_8 | 21 |
| R2 | PB9 | GPIOB_9 | 22 |
| R3 | PC14 | GPIOC_14 | 3 |
| R4 | PC15 | GPIOC_15 | 4 |

### 2.2 引脚选择理由
1. **PB12-PB15**: 连续引脚，便于批量操作
2. **PB8-PB9**: 与PB12-PB15同组，时钟统一
3. **PC14-PC15**: 避开已占用引脚，功能独立
4. **避开冲突**: 完全避开PA2,PA3,PA9,PA10和SPI引脚

## 3. 完整引脚占用表

### 3.1 已占用引脚
| 功能 | 引脚 | 状态 |
|------|------|------|
| USART1_TX | PA9 | 已占用 |
| USART1_RX | PA10 | 已占用 |
| USART2_TX | PA2 | 已占用 |
| USART2_RX | PA3 | 已占用 |
| SPI1_NSS | PA4 | 预留 |
| SPI1_SCK | PA5 | 预留 |
| SPI1_MISO | PA6 | 预留 |
| SPI1_MOSI | PA7 | 预留 |

### 3.2 矩阵键盘占用引脚
| 功能 | 引脚 | 状态 |
|------|------|------|
| 键盘C1 | PB12 | 新占用 |
| 键盘C2 | PB13 | 新占用 |
| 键盘C3 | PB14 | 新占用 |
| 键盘C4 | PB15 | 新占用 |
| 键盘R1 | PB8 | 新占用 |
| 键盘R2 | PB9 | 新占用 |
| 键盘R3 | PC14 | 新占用 |
| 键盘R4 | PC15 | 新占用 |

### 3.3 剩余可用引脚
| 引脚组 | 可用引脚 | 数量 |
|--------|----------|------|
| GPIOA | PA0,PA1,PA8,PA11,PA12,PA13,PA14,PA15 | 8个 |
| GPIOB | PB0,PB1,PB2,PB3,PB4,PB5,PB6,PB7,PB10,PB11 | 10个 |
| GPIOC | PC13 | 1个 |

## 4. 键盘布局定义

### 4.1 标准4×4键盘布局
```
    C1   C2   C3   C4
R1  [1]  [2]  [3]  [A]
R2  [4]  [5]  [6]  [B]  
R3  [7]  [8]  [9]  [C]
R4  [*]  [0]  [#]  [D]
```

### 4.2 键值映射表
| 行\列 | C1(PB12) | C2(PB13) | C3(PB14) | C4(PB15) |
|-------|----------|----------|----------|----------|
| R1(PB8) | 1 | 2 | 3 | A |
| R2(PB9) | 4 | 5 | 6 | B |
| R3(PC14) | 7 | 8 | 9 | C |
| R4(PC15) | * | 0 | # | D |

## 5. 硬件连接说明

### 5.1 连接方式
- **列线**: STM32输出 → 键盘列线 (推挽输出)
- **行线**: 键盘行线 → STM32输入 (上拉输入)
- **按键**: 行列交叉点，按下时导通

### 5.2 电路特点
- 无需外部上拉电阻 (使用内部上拉)
- 低电平有效 (按键按下时检测到低电平)
- 扫描方式: 列线逐个输出低电平，检测行线状态
