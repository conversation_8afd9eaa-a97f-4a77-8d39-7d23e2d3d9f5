# STM32到MaixCAM字符显示系统使用指南

## 1. 系统概述

### 1.1 功能描述
本系统实现STM32F103C8T6通过UART向MaixCAM发送字符数据，并在MaixCAM屏幕上实时显示。支持多种颜色、清屏、格式化文本等功能。

### 1.2 系统架构
```
STM32F103C8T6                    MaixCAM
┌─────────────────┐             ┌──────────────────┐
│  矩阵键盘输入   │             │                  │
│  传感器数据     │   UART      │   串口接收       │
│  格式化文本     │◄──────────► │   (回调函数)     │
│                 │  115200bps  │                  │
│  maixcam_comm.c │             │   屏幕显示       │
│                 │             │   多彩文本       │
└─────────────────┘             └──────────────────┘
```

## 2. 硬件连接

### 2.1 引脚连接表
| STM32引脚 | MaixCAM引脚 | 功能 | 备注 |
|-----------|-------------|------|------|
| PA2 (TX) | A17 (RX) | 数据发送 | STM32发送到MaixCAM |
| PA3 (RX) | A16 (TX) | 数据接收 | MaixCAM确认到STM32 |
| GND | GND | 公共地 | 必须连接 |
| 3.3V | 3.3V | 电源 | 可选，如果两设备独立供电 |

### 2.2 连接注意事项
1. **电平匹配**: 两设备都是3.3V，直接连接即可
2. **交叉连接**: TX连RX，RX连TX
3. **共地连接**: 必须连接GND，确保信号参考电平一致
4. **Type-C方向**: MaixCAM的Type-C正反插会影响引脚定义

## 3. 通信协议

### 3.1 数据帧格式
```
[帧头][命令][数据长度低][数据长度高][数据内容][校验和][帧尾]
 0xAA   CMD    LEN_L      LEN_H       DATA      CHK    0xFF
```

### 3.2 命令定义
| 命令码 | 功能 | 数据格式 |
|--------|------|----------|
| 0x01 | 文本显示 | [颜色][文本内容] |
| 0x02 | 清屏 | 无数据 |
| 0x03 | 设置颜色 | [颜色码] |

### 3.3 颜色定义
| 颜色码 | 颜色 | RGB值 |
|--------|------|-------|
| 0x00 | 白色 | (255,255,255) |
| 0x01 | 红色 | (255,0,0) |
| 0x02 | 绿色 | (0,255,0) |
| 0x03 | 蓝色 | (0,0,255) |
| 0x04 | 黄色 | (255,255,0) |
| 0x05 | 青色 | (0,255,255) |
| 0x06 | 洋红色 | (255,0,255) |

## 4. STM32端代码使用

### 4.1 初始化
```c
#include "maixcam_comm.h"

int main(void)
{
    // 系统初始化
    delay_init();
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2);
    uart_init(9600);  // 调试串口
    
    // MaixCAM通信初始化
    MaixCAM_UART_Init();
    
    // 发送欢迎信息
    MaixCAM_ClearScreen();
    MaixCAM_SendText("Hello MaixCAM!", MAIXCAM_COLOR_GREEN);
    
    while(1)
    {
        // 主循环代码
    }
}
```

### 4.2 基本使用示例
```c
// 发送简单文本
MaixCAM_SendText("Hello World", MAIXCAM_COLOR_WHITE);

// 发送彩色文本
MaixCAM_SendText("Red Text", MAIXCAM_COLOR_RED);
MaixCAM_SendText("Green Text", MAIXCAM_COLOR_GREEN);
MaixCAM_SendText("Blue Text", MAIXCAM_COLOR_BLUE);

// 清除屏幕
MaixCAM_ClearScreen();

// 格式化文本发送
MaixCAM_Printf("Temperature: %.1f°C", 25.6);
MaixCAM_PrintfColor(MAIXCAM_COLOR_YELLOW, "Count: %d", 123);
```

### 4.3 矩阵键盘集成示例
```c
// 在主循环中
uint8_t key = Matrix_Keyboard_GetKey();
if(key != KEY_VALUE_NONE)
{
    char key_char = Matrix_Keyboard_GetChar(key);
    
    // 根据按键显示不同内容
    if(key == KEY_VALUE_1)
    {
        MaixCAM_SendText("Task 1 Started", MAIXCAM_COLOR_GREEN);
    }
    else if(key == KEY_VALUE_2)
    {
        MaixCAM_SendText("Task 2 Started", MAIXCAM_COLOR_BLUE);
    }
    else if(key == KEY_VALUE_HASH)  // '#'键清屏
    {
        MaixCAM_ClearScreen();
    }
    else
    {
        // 显示按键信息
        char display_text[50];
        snprintf(display_text, sizeof(display_text), "Key: %c", key_char);
        MaixCAM_SendText(display_text, MAIXCAM_COLOR_WHITE);
    }
}
```

### 4.4 传感器数据显示
```c
// 定期发送传感器数据
static uint32_t last_update = 0;
if(current_time - last_update > 1000)  // 每秒更新
{
    float temperature = read_temperature();
    float humidity = read_humidity();
    
    char sensor_text[100];
    snprintf(sensor_text, sizeof(sensor_text), 
             "T:%.1f°C H:%.1f%%", temperature, humidity);
    MaixCAM_SendText(sensor_text, MAIXCAM_COLOR_CYAN);
    
    last_update = current_time;
}
```

## 5. MaixCAM端代码使用

### 5.1 程序运行
```bash
# 将maixcam_display.py上传到MaixCAM
# 在MaixCAM终端中运行
python3 maixcam_display.py
```

### 5.2 程序特性
- **实时显示**: 接收到数据立即显示
- **多行显示**: 支持多行文本滚动显示
- **彩色文本**: 支持7种颜色显示
- **自动换行**: 文本过长自动换行
- **时间戳**: 右上角显示当前时间
- **错误处理**: 完善的异常处理机制

### 5.3 显示效果
```
┌─────────────────────────────────┐
│ MaixCAM Ready              12:34 │
│ STM32 Connected!               │
│ Key Pressed: A                 │
│ Temperature: 25.6°C            │
│ Task 1 Started                 │
│                                │
└─────────────────────────────────┘
```

## 6. API函数参考

### 6.1 STM32端API

#### 6.1.1 初始化函数
```c
void MaixCAM_UART_Init(void);
```
- **功能**: 初始化MaixCAM通信串口
- **参数**: 无
- **返回**: 无

#### 6.1.2 文本发送函数
```c
void MaixCAM_SendText(char* text, uint8_t color);
```
- **功能**: 发送文本到MaixCAM显示
- **参数**: text-文本内容, color-颜色码
- **返回**: 无

#### 6.1.3 格式化发送宏
```c
MaixCAM_Printf(fmt, ...);
MaixCAM_PrintfColor(color, fmt, ...);
```
- **功能**: 格式化文本发送
- **参数**: fmt-格式字符串, ...-参数列表
- **返回**: 无

#### 6.1.4 屏幕控制函数
```c
void MaixCAM_ClearScreen(void);
void MaixCAM_SetTextColor(uint8_t color);
```
- **功能**: 清屏和设置默认颜色
- **参数**: color-颜色码
- **返回**: 无

### 6.2 MaixCAM端主要类

#### 6.2.1 STM32DisplayReceiver类
```python
class STM32DisplayReceiver:
    def __init__(self):           # 初始化
    def on_data_received(self, serial, data):  # 数据接收回调
    def add_text_line(self, text, color):      # 添加文本行
    def clear_screen(self):       # 清屏
    def update_display(self):     # 更新显示
    def run(self):               # 运行主循环
```

## 7. 调试和故障排除

### 7.1 常见问题

#### 7.1.1 无法通信
- **检查连接**: 确认TX/RX交叉连接，GND连接
- **检查波特率**: 确认两端波特率一致(115200)
- **检查引脚**: 确认引脚配置正确

#### 7.1.2 显示异常
- **字符乱码**: 检查编码格式，确保UTF-8
- **颜色错误**: 检查颜色码定义
- **显示不全**: 检查文本长度限制

#### 7.1.3 数据丢失
- **缓冲区溢出**: 减少发送频率
- **校验错误**: 检查数据完整性
- **中断冲突**: 检查中断优先级设置

### 7.2 调试方法

#### 7.2.1 STM32端调试
```c
// 添加调试输出
printf("发送到MaixCAM: %s\r\n", text);

// 检查发送状态
if(MaixCAM_WaitACK(1000))
{
    printf("MaixCAM确认收到\r\n");
}
else
{
    printf("MaixCAM无响应\r\n");
}
```

#### 7.2.2 MaixCAM端调试
```python
# 添加调试输出
print(f"接收数据: {data.hex()}")
print(f"解析文本: {text}")
print(f"显示颜色: {color}")
```

## 8. 扩展功能

### 8.1 支持图像显示
- 扩展协议支持图像数据传输
- 实现简单图形绘制功能
- 支持图标和符号显示

### 8.2 支持触摸交互
- MaixCAM触摸屏反馈到STM32
- 实现双向交互控制
- 支持菜单选择功能

### 8.3 数据记录功能
- 记录显示历史
- 支持数据导出
- 实现日志功能

## 9. 性能优化

### 9.1 通信优化
- 使用DMA传输减少CPU占用
- 实现数据压缩减少传输量
- 优化协议减少开销

### 9.2 显示优化
- 使用双缓冲减少闪烁
- 优化字体渲染提高速度
- 实现局部刷新减少功耗

### 9.3 内存优化
- 合理管理缓冲区大小
- 及时释放不用的资源
- 避免内存泄漏
