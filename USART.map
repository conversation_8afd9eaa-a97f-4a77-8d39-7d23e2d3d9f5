Component: ARM Compiler 5.06 update 7 (build 960) Tool: armlink [4d3601]

==============================================================================

Section Cross References

    main.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    main.o(.text) refers to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    main.o(.text) refers to printf1.o(x$fpl$printf1) for _printf_fp_dec
    main.o(.text) refers to delay.o(.text) for delay_init
    main.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    main.o(.text) refers to usart.o(.text) for uart_init
    main.o(.text) refers to camera.o(.text) for Camera_Init
    main.o(.text) refers to fflt_clz.o(x$fpl$fflt) for __aeabi_i2f
    main.o(.text) refers to fdiv.o(x$fpl$fdiv) for __aeabi_fdiv
    main.o(.text) refers to f2d.o(x$fpl$f2d) for __aeabi_f2d
    main.o(.text) refers to __2printf.o(.text) for __2printf
    main.o(.text) refers to main.o(.data) for D
    main.o(.text) refers to camera.o(.data) for Camera_RxPacket
    system_stm32f10x.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    led.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    led.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    matrix_keyboard.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    matrix_keyboard.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    matrix_keyboard.o(.text) refers to delay.o(.text) for delay_us
    matrix_keyboard.o(.text) refers to matrix_keyboard.o(.bss) for key_state
    matrix_keyboard.o(.text) refers to matrix_keyboard.o(.data) for last_key
    delay.o(.text) refers to misc.o(.text) for SysTick_CLKSourceConfig
    delay.o(.text) refers to system_stm32f10x.o(.data) for SystemCoreClock
    delay.o(.text) refers to delay.o(.data) for fac_us
    usart.o(.text) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphClockCmd
    usart.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    usart.o(.text) refers to misc.o(.text) for NVIC_Init
    usart.o(.text) refers to stm32f10x_usart.o(.text) for USART_Init
    usart.o(.text) refers to usart.o(.data) for USART_RX_STA
    usart.o(.text) refers to usart.o(.bss) for USART_RX_BUF
    usart.o(.bss) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    usart.o(.data) refers (Special) to use_no_semi_2.o(.text) for __use_no_semihosting
    camera.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB1PeriphClockCmd
    camera.o(.text) refers to stm32f10x_gpio.o(.text) for GPIO_Init
    camera.o(.text) refers to stm32f10x_usart.o(.text) for USART_Init
    camera.o(.text) refers to misc.o(.text) for NVIC_PriorityGroupConfig
    camera.o(.text) refers to vsprintf.o(.text) for vsprintf
    camera.o(.text) refers to camera.o(.data) for Camera_RxFlag
    startup_stm32f10x_hd.o(STACK) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(HEAP) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(STACK) for __initial_sp
    startup_stm32f10x_hd.o(RESET) refers to startup_stm32f10x_hd.o(.text) for Reset_Handler
    startup_stm32f10x_hd.o(RESET) refers to stm32f10x_it.o(.text) for NMI_Handler
    startup_stm32f10x_hd.o(RESET) refers to usart.o(.text) for USART1_IRQHandler
    startup_stm32f10x_hd.o(RESET) refers to camera.o(.text) for USART2_IRQHandler
    startup_stm32f10x_hd.o(.text) refers (Special) to heapauxi.o(.text) for __use_two_region_memory
    startup_stm32f10x_hd.o(.text) refers to system_stm32f10x.o(.text) for SystemInit
    startup_stm32f10x_hd.o(.text) refers to __main.o(!!!main) for __main
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(HEAP) for Heap_Mem
    startup_stm32f10x_hd.o(.text) refers to startup_stm32f10x_hd.o(STACK) for Stack_Mem
    stm32f10x_gpio.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    stm32f10x_rcc.o(.text) refers to stm32f10x_rcc.o(.data) for APBAHBPrescTable
    stm32f10x_usart.o(.text) refers to stm32f10x_rcc.o(.text) for RCC_APB2PeriphResetCmd
    use_no_semi_2.o(.text) refers (Special) to use_no_semi.o(.text) for __use_no_semihosting_swi
    vsprintf.o(.text) refers (Special) to _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) for _printf_a
    vsprintf.o(.text) refers (Special) to _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) for _printf_c
    vsprintf.o(.text) refers (Special) to _printf_charcount.o(.text) for _printf_charcount
    vsprintf.o(.text) refers (Special) to _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) for _printf_d
    vsprintf.o(.text) refers (Special) to _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) for _printf_e
    vsprintf.o(.text) refers (Special) to _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) for _printf_f
    vsprintf.o(.text) refers (Special) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    vsprintf.o(.text) refers (Special) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    vsprintf.o(.text) refers (Special) to _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) for _printf_g
    vsprintf.o(.text) refers (Special) to _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) for _printf_i
    vsprintf.o(.text) refers (Special) to _printf_dec.o(.text) for _printf_int_dec
    vsprintf.o(.text) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    vsprintf.o(.text) refers (Special) to _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) for _printf_lc
    vsprintf.o(.text) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    vsprintf.o(.text) refers (Special) to _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) for _printf_lld
    vsprintf.o(.text) refers (Special) to _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) for _printf_lli
    vsprintf.o(.text) refers (Special) to _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) for _printf_llo
    vsprintf.o(.text) refers (Special) to _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) for _printf_llu
    vsprintf.o(.text) refers (Special) to _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) for _printf_llx
    vsprintf.o(.text) refers (Special) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    vsprintf.o(.text) refers (Special) to _printf_hex_int_ll_ptr.o(.text) for _printf_longlong_hex
    vsprintf.o(.text) refers (Special) to _printf_oct_int_ll.o(.text) for _printf_longlong_oct
    vsprintf.o(.text) refers (Special) to _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) for _printf_ls
    vsprintf.o(.text) refers (Special) to _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) for _printf_n
    vsprintf.o(.text) refers (Special) to _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) for _printf_o
    vsprintf.o(.text) refers (Special) to _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) for _printf_p
    vsprintf.o(.text) refers (Special) to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    vsprintf.o(.text) refers (Special) to _printf_pad.o(.text) for _printf_post_padding
    vsprintf.o(.text) refers (Special) to _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) for _printf_s
    vsprintf.o(.text) refers (Special) to _printf_str.o(.text) for _printf_str
    vsprintf.o(.text) refers (Special) to _printf_truncate.o(.text) for _printf_truncate_signed
    vsprintf.o(.text) refers (Special) to _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) for _printf_u
    vsprintf.o(.text) refers (Special) to _printf_wctomb.o(.text) for _printf_wctomb
    vsprintf.o(.text) refers (Special) to _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) for _printf_x
    vsprintf.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    vsprintf.o(.text) refers to _sputc.o(.text) for _sputc
    __2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    __2printf.o(.text) refers to usart.o(.data) for __stdout
    noretval__2printf.o(.text) refers to _printf_char_file.o(.text) for _printf_char_file
    noretval__2printf.o(.text) refers to usart.o(.data) for __stdout
    __printf.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags.o(.text) refers to __printf_flags.o(.constdata) for .constdata
    __printf_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss.o(.text) refers to __printf_flags_ss.o(.constdata) for .constdata
    __printf_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_wp.o(.text) refers to __printf_flags_wp.o(.constdata) for .constdata
    __printf_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_wp.o(i._is_digit) for _is_digit
    __printf_flags_ss_wp.o(.text) refers to _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) for _printf_percent
    __printf_flags_ss_wp.o(.text) refers to __printf_flags_ss_wp.o(.constdata) for .constdata
    _printf_f.o(.ARM.Collect$$_printf_percent$$00000003) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000) refers (Special) to _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017) for _printf_percent_end
    __main.o(!!!main) refers to __rtentry.o(.ARM.Collect$$rtentry$$00000000) for __rt_entry
    f2d.o(x$fpl$f2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    f2d.o(x$fpl$f2d) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    f2d.o(x$fpl$f2d) refers to dretinf.o(x$fpl$dretinf) for __fpl_dretinf
    fdiv.o(x$fpl$frdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$frdiv) refers to fdiv.o(x$fpl$fdiv) for _fdiv1
    fdiv.o(x$fpl$fdiv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fdiv.o(x$fpl$fdiv) refers to fretinf.o(x$fpl$fretinf) for __fpl_fretinf
    fdiv.o(x$fpl$fdiv) refers to fnaninf.o(x$fpl$fnaninf) for __fpl_fnaninf
    fflt_clz.o(x$fpl$ffltu) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$fflt) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fflt_clz.o(x$fpl$ffltn) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf1.o(x$fpl$printf1) refers to _printf_fp_dec.o(.text) for _printf_fp_dec_real
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for __rt_entry_li
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for __rt_entry_main
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$0000000C) for __rt_entry_postli_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000009) for __rt_entry_postsh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry2.o(.ARM.Collect$$rtentry$$00000002) for __rt_entry_presh_1
    __rtentry.o(.ARM.Collect$$rtentry$$00000000) refers (Special) to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for __rt_entry_sh
    _printf_str.o(.text) refers (Special) to _printf_char.o(.text) for _printf_cs_common
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_str.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_signed
    _printf_dec.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_fp_dec.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_dec.o(.text) refers (Special) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    _printf_fp_dec.o(.text) refers to bigflt0.o(.text) for _btod_etento
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_d2e) for _btod_d2e
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    _printf_fp_dec.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    _printf_fp_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_fp_dec.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_dec.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_dec.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    _printf_fp_dec.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char_common.o(.text) refers to __printf_flags_ss_wp.o(.text) for __printf
    _printf_char_file.o(.text) refers to _printf_char_common.o(.text) for _printf_char_common
    _printf_char_file.o(.text) refers to ferror.o(.text) for ferror
    _printf_char_file.o(.text) refers to usart.o(.text) for fputc
    _printf_wctomb.o(.text) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_wctomb.o(.text) refers to _wcrtomb.o(.text) for _wcrtomb
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_wctomb.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_wctomb.o(.text) refers to _printf_wctomb.o(.constdata) for .constdata
    _printf_wctomb.o(.constdata) refers (Special) to _printf_wchar.o(.text) for _printf_lcs_common
    _printf_longlong_dec.o(.text) refers to lludiv10.o(.text) for _ll_udiv10
    _printf_longlong_dec.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_oct_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_oct_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll.o(.text) refers to _printf_hex_ll.o(.constdata) for .constdata
    _printf_hex_int.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int.o(.text) refers to _printf_hex_int.o(.constdata) for .constdata
    _printf_hex_int_ll.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll.o(.text) refers to _printf_hex_int_ll.o(.constdata) for .constdata
    _printf_hex_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ptr.o(.text) refers to _printf_hex_ptr.o(.constdata) for .constdata
    _printf_hex_int_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ptr.o(.text) refers to _printf_hex_int_ptr.o(.constdata) for .constdata
    _printf_hex_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_ll_ptr.o(.text) refers to _printf_hex_ll_ptr.o(.constdata) for .constdata
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_intcommon.o(.text) for _printf_int_common
    _printf_hex_int_ll_ptr.o(.text) refers (Weak) to _printf_truncate.o(.text) for _printf_truncate_unsigned
    _printf_hex_int_ll_ptr.o(.text) refers to _printf_hex_int_ll_ptr.o(.constdata) for .constdata
    _printf_c.o(.ARM.Collect$$_printf_percent$$00000013) refers (Weak) to _printf_char.o(.text) for _printf_char
    _printf_s.o(.ARM.Collect$$_printf_percent$$00000014) refers (Weak) to _printf_char.o(.text) for _printf_string
    _printf_n.o(.ARM.Collect$$_printf_percent$$00000001) refers (Weak) to _printf_charcount.o(.text) for _printf_charcount
    _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_int_hex
    _printf_p.o(.ARM.Collect$$_printf_percent$$00000002) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_hex_ptr
    _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_int_oct
    _printf_i.o(.ARM.Collect$$_printf_percent$$00000008) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_d.o(.ARM.Collect$$_printf_percent$$00000009) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A) refers (Weak) to _printf_dec.o(.text) for _printf_int_dec
    _printf_e.o(.ARM.Collect$$_printf_percent$$00000004) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_g.o(.ARM.Collect$$_printf_percent$$00000005) refers (Weak) to printf1.o(x$fpl$printf1) for _printf_fp_dec
    _printf_a.o(.ARM.Collect$$_printf_percent$$00000006) refers (Weak) to printf2.o(x$fpl$printf2) for _printf_fp_hex
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F) refers (Weak) to _printf_longlong_dec.o(.text) for _printf_longlong_dec
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015) refers (Weak) to _printf_wchar.o(.text) for _printf_wchar
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Special) to _printf_l.o(.ARM.Collect$$_printf_percent$$00000012) for _printf_l
    _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016) refers (Weak) to _printf_wchar.o(.text) for _printf_wstring
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010) refers (Weak) to _printf_oct_int_ll.o(.text) for _printf_ll_oct
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Special) to _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007) for _printf_ll
    _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011) refers (Weak) to _printf_hex_int_ll_ptr.o(.text) for _printf_ll_hex
    dretinf.o(x$fpl$dretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fnaninf.o(x$fpl$fnaninf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fretinf.o(x$fpl$fretinf) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    printf2.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    printf2b.o(x$fpl$printf2) refers to _printf_fp_hex.o(.text) for _printf_fp_hex_real
    __rtentry2.o(.ARM.Collect$$rtentry$$00000008) refers to boardinit2.o(.text) for _platform_post_stackheap_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) refers to libinit.o(.ARM.Collect$$libinit$$00000000) for __rt_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) refers to boardinit3.o(.text) for _platform_post_lib_init
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to main.o(.text) for main
    __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) refers to exit.o(.text) for exit
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000001) for .ARM.Collect$$rtentry$$00000001
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$00000008) for .ARM.Collect$$rtentry$$00000008
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000A) for .ARM.Collect$$rtentry$$0000000A
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000B) for .ARM.Collect$$rtentry$$0000000B
    __rtentry2.o(.ARM.exidx) refers to __rtentry2.o(.ARM.Collect$$rtentry$$0000000D) for .ARM.Collect$$rtentry$$0000000D
    __rtentry4.o(.ARM.Collect$$rtentry$$00000004) refers to sys_stackheap_outer.o(.text) for __user_setup_stackheap
    __rtentry4.o(.ARM.exidx) refers to __rtentry4.o(.ARM.Collect$$rtentry$$00000004) for .ARM.Collect$$rtentry$$00000004
    rt_locale.o(.text) refers to rt_locale.o(.bss) for __rt_locale_data
    rt_locale_intlibspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_intcommon.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_hex.o(.text) refers to fpclassify.o(i.__ARM_fpclassify) for __ARM_fpclassify
    _printf_fp_hex.o(.text) refers to _printf_fp_infnan.o(.text) for _printf_fp_infnan
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_hex.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_fp_hex.o(.text) refers to _printf_fp_hex.o(.constdata) for .constdata
    _printf_fp_hex.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_pre_padding
    _printf_fp_infnan.o(.text) refers (Weak) to _printf_pad.o(.text) for _printf_post_padding
    _printf_char.o(.text) refers (Weak) to _printf_str.o(.text) for _printf_str
    _printf_wchar.o(.text) refers (Weak) to _printf_wctomb.o(.text) for _printf_wctomb
    bigflt0.o(.text) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    bigflt0.o(.text) refers to btod.o(CL$$btod_emul) for _btod_emul
    bigflt0.o(.text) refers to btod.o(CL$$btod_ediv) for _btod_ediv
    bigflt0.o(.text) refers to bigflt0.o(.constdata) for .constdata
    bigflt0.o(.constdata) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e) refers to btod.o(CL$$btod_d2e_norm_op1) for _d2e_norm_op1
    btod.o(CL$$btod_d2e_norm_op1) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_d2e_norm_op1) refers to btod.o(CL$$btod_d2e_denorm_low) for _d2e_denorm_low
    btod.o(CL$$btod_d2e_denorm_low) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emul) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_ediv) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_ediv) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_emuld) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_mult_common) for __btod_mult_common
    btod.o(CL$$btod_emuld) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_edivd) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_div_common) for __btod_div_common
    btod.o(CL$$btod_edivd) refers to btod.o(CL$$btod_e2d) for _e2d
    btod.o(CL$$btod_e2e) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_e2d) refers to btod.o(CL$$btod_e2e) for _e2e
    btod.o(CL$$btod_mult_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    btod.o(CL$$btod_div_common) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    _wcrtomb.o(.text) refers to rt_ctype_table.o(.text) for __rt_ctype_table
    lc_numeric_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000016) for __rt_lib_init_lc_numeric_2
    lc_numeric_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_numeric_c.o(locale$$code) refers to lc_numeric_c.o(locale$$data) for __lcnum_c_name
    istatus.o(x$fpl$ieeestatus) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    fpclassify.o(i.__ARM_fpclassify) refers (Special) to usenofp.o(x$fpl$usenofp) for __I$use$fp
    libspace.o(.text) refers to libspace.o(.bss) for __libspace_start
    sys_stackheap_outer.o(.text) refers to libspace.o(.text) for __user_perproc_libspace
    sys_stackheap_outer.o(.text) refers to startup_stm32f10x_hd.o(.text) for __user_initial_stackheap
    rt_ctype_table.o(.text) refers to rt_locale_intlibspace.o(.text) for __rt_locale
    rt_ctype_table.o(.text) refers to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    exit.o(.text) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for __rt_exit
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002E) for __rt_lib_init_alloca_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000002C) for __rt_lib_init_argv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001B) for __rt_lib_init_atexit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000021) for __rt_lib_init_clock_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000032) for __rt_lib_init_cpp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000030) for __rt_lib_init_exceptions_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000002) for __rt_lib_init_fp_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001F) for __rt_lib_init_fp_trap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000023) for __rt_lib_init_getenv_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000A) for __rt_lib_init_heap_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000011) for __rt_lib_init_lc_collate_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000013) for __rt_lib_init_lc_ctype_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000015) for __rt_lib_init_lc_monetary_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000017) for __rt_lib_init_lc_numeric_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000019) for __rt_lib_init_lc_time_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000004) for __rt_lib_init_preinit_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000E) for __rt_lib_init_rand_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000033) for __rt_lib_init_return
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000001D) for __rt_lib_init_signal_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000025) for __rt_lib_init_stdio_1
    libinit.o(.ARM.Collect$$libinit$$00000000) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$0000000C) for __rt_lib_init_user_alloc_1
    libinit2.o(.ARM.Collect$$libinit$$0000000F) refers (Weak) to rt_locale_intlibspace.o(.text) for __rt_locale
    libinit2.o(.ARM.Collect$$libinit$$00000010) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000012) refers (Weak) to lc_ctype_c.o(locale$$code) for _get_lc_ctype
    libinit2.o(.ARM.Collect$$libinit$$00000014) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000016) refers (Weak) to lc_numeric_c.o(locale$$code) for _get_lc_numeric
    libinit2.o(.ARM.Collect$$libinit$$00000018) refers to libinit2.o(.ARM.Collect$$libinit$$0000000F) for .ARM.Collect$$libinit$$0000000F
    libinit2.o(.ARM.Collect$$libinit$$00000026) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    libinit2.o(.ARM.Collect$$libinit$$00000027) refers to argv_veneer.o(.emb_text) for __ARM_argv_veneer
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.Collect$$rtexit$$00000000) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for __rt_exit_exit
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for __rt_exit_ls
    rtexit.o(.ARM.exidx) refers (Special) to rtexit2.o(.ARM.Collect$$rtexit$$00000002) for __rt_exit_prels_1
    rtexit.o(.ARM.exidx) refers to rtexit.o(.ARM.Collect$$rtexit$$00000000) for .ARM.Collect$$rtexit$$00000000
    lc_ctype_c.o(locale$$data) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers (Special) to libinit2.o(.ARM.Collect$$libinit$$00000012) for __rt_lib_init_lc_ctype_2
    lc_ctype_c.o(locale$$code) refers to strcmpv7m.o(.text) for strcmp
    lc_ctype_c.o(locale$$code) refers to lc_ctype_c.o(locale$$data) for __lcctype_c_name
    argv_veneer.o(.emb_text) refers to no_argv.o(.text) for __ARM_get_argv
    rtexit2.o(.ARM.Collect$$rtexit$$00000003) refers to libshutdown.o(.ARM.Collect$$libshutdown$$00000000) for __rt_lib_shutdown
    rtexit2.o(.ARM.Collect$$rtexit$$00000004) refers to usart.o(.text) for _sys_exit
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000001) for .ARM.Collect$$rtexit$$00000001
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000003) for .ARM.Collect$$rtexit$$00000003
    rtexit2.o(.ARM.exidx) refers to rtexit2.o(.ARM.Collect$$rtexit$$00000004) for .ARM.Collect$$rtexit$$00000004
    _get_argv_nomalloc.o(.text) refers (Special) to hrguard.o(.text) for __heap_region$guard
    _get_argv_nomalloc.o(.text) refers to defsig_rtmem_outer.o(.text) for __rt_SIGRTMEM
    _get_argv_nomalloc.o(.text) refers to sys_command.o(.text) for _sys_command_string
    sys_command.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_command.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig_rtmem_outer.o(.text) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_rtmem_outer.o(.text) refers to defsig_exit.o(.text) for __sig_exit
    defsig_rtmem_formal.o(.text) refers to rt_raise.o(.text) for __rt_raise
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000004) for __rt_lib_shutdown_cpp_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000002) for __rt_lib_shutdown_fini_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000009) for __rt_lib_shutdown_fp_trap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000011) for __rt_lib_shutdown_heap_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000012) for __rt_lib_shutdown_return
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C) for __rt_lib_shutdown_signal_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$00000006) for __rt_lib_shutdown_stdio_1
    libshutdown.o(.ARM.Collect$$libshutdown$$00000000) refers (Special) to libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E) for __rt_lib_shutdown_user_alloc_1
    rt_raise.o(.text) refers to __raise.o(.text) for __raise
    rt_raise.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_exit.o(.text) refers to usart.o(.text) for _sys_exit
    defsig_rtmem_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    __raise.o(.text) refers to defsig.o(CL$$defsig) for __default_signal_handler
    defsig_general.o(.text) refers to sys_wrch.o(.text) for _ttywrch
    sys_wrch.o(.text) refers (Special) to use_no_semi.o(.text) for __I$use$semihosting
    sys_wrch.o(.text) refers (Special) to indicate_semi.o(.text) for __semihosting_library_function
    defsig.o(CL$$defsig) refers to defsig_rtmem_inner.o(.text) for __rt_SIGRTMEM_inner
    defsig_abrt_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_fpe_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_rtred_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_stak_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_pvfn_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_cppl_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_segv_inner.o(.text) refers to defsig_general.o(.text) for __default_signal_display
    defsig_other.o(.text) refers to defsig_general.o(.text) for __default_signal_display


==============================================================================

Removing Unused input sections from the image.

    Removing led.o(.text), (80 bytes).
    Removing matrix_keyboard.o(.text), (452 bytes).
    Removing matrix_keyboard.o(.bss), (16 bytes).
    Removing matrix_keyboard.o(.data), (2 bytes).
    Removing sys.o(.emb_text), (6 bytes).
    Removing sys.o(.text), (12 bytes).
    Removing core_cm3.o(.emb_text), (32 bytes).

7 unused section(s) (total 600 bytes) removed from the image.

==============================================================================

Image Symbol Table

    Local Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit2.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit3.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardshut.o ABSOLUTE
    ../clib/angel/boardlib.s                 0x00000000   Number         0  boardinit1.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_zi.o ABSOLUTE
    ../clib/angel/handlers.s                 0x00000000   Number         0  __scatter_copy.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry4.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  __rtentry2.o ABSOLUTE
    ../clib/angel/kernel.s                   0x00000000   Number         0  rtexit2.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale_intlibspace.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_raise.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_locale.o ABSOLUTE
    ../clib/angel/rt.s                       0x00000000   Number         0  rt_ctype_table.o ABSOLUTE
    ../clib/angel/scatter.s                  0x00000000   Number         0  __scatter.o ABSOLUTE
    ../clib/angel/startup.s                  0x00000000   Number         0  __main.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  indicate_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  libspace.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  sys_stackheap_outer.o ABSOLUTE
    ../clib/angel/sys.s                      0x00000000   Number         0  use_no_semi_2.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_wrch.o ABSOLUTE
    ../clib/angel/sysapp.c                   0x00000000   Number         0  sys_command.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  _get_argv_nomalloc.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  argv_veneer.o ABSOLUTE
    ../clib/armsys.c                         0x00000000   Number         0  no_argv.o ABSOLUTE
    ../clib/bigflt.c                         0x00000000   Number         0  bigflt0.o ABSOLUTE
    ../clib/btod.s                           0x00000000   Number         0  btod.o ABSOLUTE
    ../clib/heapalloc.c                      0x00000000   Number         0  hrguard.o ABSOLUTE
    ../clib/heapaux.c                        0x00000000   Number         0  heapauxi.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libshutdown2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit2.o ABSOLUTE
    ../clib/libinit.s                        0x00000000   Number         0  libinit.o ABSOLUTE
    ../clib/locale.c                         0x00000000   Number         0  _wcrtomb.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_numeric_c.o ABSOLUTE
    ../clib/locale.s                         0x00000000   Number         0  lc_ctype_c.o ABSOLUTE
    ../clib/longlong.s                       0x00000000   Number         0  lludiv10.o ABSOLUTE
    ../clib/memcpset.s                       0x00000000   Number         0  strcmpv7m.o ABSOLUTE
    ../clib/misc.s                           0x00000000   Number         0  printf_stubs.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _sputc.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_hex_int_ll_ptr.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wchar.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  vsprintf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_hex.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_intcommon.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  noretval__2printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_int.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_oct_ll.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_pad.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_str.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_longlong_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_charcount.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_nopercent.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_flags_ss_wp.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_dec.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_common.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_wctomb.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_char_file.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_truncate.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  _printf_fp_infnan.o ABSOLUTE
    ../clib/printf.c                         0x00000000   Number         0  __printf_ss.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llx.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_c.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_s.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_n.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_x.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_p.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_o.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_i.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_f.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_d.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_u.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_e.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_g.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_a.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_percent_end.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lli.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lld.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llu.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ll.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_l.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_lc.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_ls.o ABSOLUTE
    ../clib/printf_percent.s                 0x00000000   Number         0  _printf_llo.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_formal.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_exit.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_fpe_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_other.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  __raise.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_segv_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtmem_outer.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_cppl_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_pvfn_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_general.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_stak_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_rtred_inner.o ABSOLUTE
    ../clib/signal.c                         0x00000000   Number         0  defsig_abrt_inner.o ABSOLUTE
    ../clib/signal.s                         0x00000000   Number         0  defsig.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror_locked.o ABSOLUTE
    ../clib/stdio.c                          0x00000000   Number         0  ferror.o ABSOLUTE
    ../clib/stdlib.c                         0x00000000   Number         0  exit.o ABSOLUTE
    ../fplib/dretinf.s                       0x00000000   Number         0  dretinf.o ABSOLUTE
    ../fplib/f2d.s                           0x00000000   Number         0  f2d.o ABSOLUTE
    ../fplib/fdiv.s                          0x00000000   Number         0  fdiv.o ABSOLUTE
    ../fplib/fflt.s                          0x00000000   Number         0  fflt_clz.o ABSOLUTE
    ../fplib/fnaninf.s                       0x00000000   Number         0  fnaninf.o ABSOLUTE
    ../fplib/fpinit.s                        0x00000000   Number         0  fpinit.o ABSOLUTE
    ../fplib/fretinf.s                       0x00000000   Number         0  fretinf.o ABSOLUTE
    ../fplib/istatus.s                       0x00000000   Number         0  istatus.o ABSOLUTE
    ../fplib/printf1.s                       0x00000000   Number         0  printf1.o ABSOLUTE
    ../fplib/printf2.s                       0x00000000   Number         0  printf2.o ABSOLUTE
    ../fplib/printf2a.s                      0x00000000   Number         0  printf2a.o ABSOLUTE
    ../fplib/printf2b.s                      0x00000000   Number         0  printf2b.o ABSOLUTE
    ../fplib/usenofp.s                       0x00000000   Number         0  usenofp.o ABSOLUTE
    ../mathlib/fpclassify.c                  0x00000000   Number         0  fpclassify.o ABSOLUTE
    ..\CORE\core_cm3.c                       0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\CORE\startup_stm32f10x_hd.s           0x00000000   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    ..\HARDWARE\KEY\matrix_keyboard.c        0x00000000   Number         0  matrix_keyboard.o ABSOLUTE
    ..\HARDWARE\LED\led.c                    0x00000000   Number         0  led.o ABSOLUTE
    ..\STM32F10x_FWLib\src\misc.c            0x00000000   Number         0  misc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_gpio.c  0x00000000   Number         0  stm32f10x_gpio.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_rcc.c   0x00000000   Number         0  stm32f10x_rcc.o ABSOLUTE
    ..\STM32F10x_FWLib\src\stm32f10x_usart.c 0x00000000   Number         0  stm32f10x_usart.o ABSOLUTE
    ..\SYSTEM\delay\delay.c                  0x00000000   Number         0  delay.o ABSOLUTE
    ..\SYSTEM\sys\sys.c                      0x00000000   Number         0  sys.o ABSOLUTE
    ..\SYSTEM\usart\Camera.c                 0x00000000   Number         0  camera.o ABSOLUTE
    ..\SYSTEM\usart\usart.c                  0x00000000   Number         0  usart.o ABSOLUTE
    ..\\CORE\\core_cm3.c                     0x00000000   Number         0  core_cm3.o ABSOLUTE
    ..\\SYSTEM\\sys\\sys.c                   0x00000000   Number         0  sys.o ABSOLUTE
    dc.s                                     0x00000000   Number         0  dc.o ABSOLUTE
    main.c                                   0x00000000   Number         0  main.o ABSOLUTE
    stm32f10x_it.c                           0x00000000   Number         0  stm32f10x_it.o ABSOLUTE
    system_stm32f10x.c                       0x00000000   Number         0  system_stm32f10x.o ABSOLUTE
    RESET                                    0x08000000   Section      304  startup_stm32f10x_hd.o(RESET)
    !!!main                                  0x08000130   Section        8  __main.o(!!!main)
    !!!scatter                               0x08000138   Section       52  __scatter.o(!!!scatter)
    !!handler_copy                           0x0800016c   Section       26  __scatter_copy.o(!!handler_copy)
    !!handler_zi                             0x08000188   Section       28  __scatter_zi.o(!!handler_zi)
    .ARM.Collect$$_printf_percent$$00000000  0x080001a4   Section        0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    .ARM.Collect$$_printf_percent$$00000001  0x080001a4   Section        6  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    .ARM.Collect$$_printf_percent$$00000002  0x080001aa   Section        6  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    .ARM.Collect$$_printf_percent$$00000003  0x080001b0   Section        6  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    .ARM.Collect$$_printf_percent$$00000004  0x080001b6   Section        6  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    .ARM.Collect$$_printf_percent$$00000005  0x080001bc   Section        6  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    .ARM.Collect$$_printf_percent$$00000006  0x080001c2   Section        6  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    .ARM.Collect$$_printf_percent$$00000007  0x080001c8   Section       10  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    .ARM.Collect$$_printf_percent$$00000008  0x080001d2   Section        6  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    .ARM.Collect$$_printf_percent$$00000009  0x080001d8   Section        6  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    .ARM.Collect$$_printf_percent$$0000000A  0x080001de   Section        6  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    .ARM.Collect$$_printf_percent$$0000000B  0x080001e4   Section        6  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    .ARM.Collect$$_printf_percent$$0000000C  0x080001ea   Section        6  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    .ARM.Collect$$_printf_percent$$0000000D  0x080001f0   Section        6  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    .ARM.Collect$$_printf_percent$$0000000E  0x080001f6   Section        6  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    .ARM.Collect$$_printf_percent$$0000000F  0x080001fc   Section        6  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    .ARM.Collect$$_printf_percent$$00000010  0x08000202   Section        6  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    .ARM.Collect$$_printf_percent$$00000011  0x08000208   Section        6  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    .ARM.Collect$$_printf_percent$$00000012  0x0800020e   Section       10  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    .ARM.Collect$$_printf_percent$$00000013  0x08000218   Section        6  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    .ARM.Collect$$_printf_percent$$00000014  0x0800021e   Section        6  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    .ARM.Collect$$_printf_percent$$00000015  0x08000224   Section        6  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    .ARM.Collect$$_printf_percent$$00000016  0x0800022a   Section        6  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    .ARM.Collect$$_printf_percent$$00000017  0x08000230   Section        4  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    .ARM.Collect$$libinit$$00000000          0x08000234   Section        2  libinit.o(.ARM.Collect$$libinit$$00000000)
    .ARM.Collect$$libinit$$00000002          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    .ARM.Collect$$libinit$$00000004          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    .ARM.Collect$$libinit$$0000000A          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    .ARM.Collect$$libinit$$0000000C          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    .ARM.Collect$$libinit$$0000000E          0x08000236   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    .ARM.Collect$$libinit$$0000000F          0x08000236   Section        6  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    .ARM.Collect$$libinit$$00000011          0x0800023c   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    .ARM.Collect$$libinit$$00000012          0x0800023c   Section       12  libinit2.o(.ARM.Collect$$libinit$$00000012)
    .ARM.Collect$$libinit$$00000013          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    .ARM.Collect$$libinit$$00000015          0x08000248   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    .ARM.Collect$$libinit$$00000016          0x08000248   Section       10  libinit2.o(.ARM.Collect$$libinit$$00000016)
    .ARM.Collect$$libinit$$00000017          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    .ARM.Collect$$libinit$$00000019          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    .ARM.Collect$$libinit$$0000001B          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    .ARM.Collect$$libinit$$0000001D          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    .ARM.Collect$$libinit$$0000001F          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    .ARM.Collect$$libinit$$00000021          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    .ARM.Collect$$libinit$$00000023          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    .ARM.Collect$$libinit$$00000025          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    .ARM.Collect$$libinit$$0000002C          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    .ARM.Collect$$libinit$$0000002E          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    .ARM.Collect$$libinit$$00000030          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    .ARM.Collect$$libinit$$00000032          0x08000252   Section        0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    .ARM.Collect$$libinit$$00000033          0x08000252   Section        2  libinit2.o(.ARM.Collect$$libinit$$00000033)
    .ARM.Collect$$libshutdown$$00000000      0x08000254   Section        2  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    .ARM.Collect$$libshutdown$$00000002      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    .ARM.Collect$$libshutdown$$00000004      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    .ARM.Collect$$libshutdown$$00000006      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    .ARM.Collect$$libshutdown$$00000009      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    .ARM.Collect$$libshutdown$$0000000C      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    .ARM.Collect$$libshutdown$$0000000E      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    .ARM.Collect$$libshutdown$$00000011      0x08000256   Section        0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    .ARM.Collect$$libshutdown$$00000012      0x08000256   Section        2  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    .ARM.Collect$$rtentry$$00000000          0x08000258   Section        0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    .ARM.Collect$$rtentry$$00000002          0x08000258   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    .ARM.Collect$$rtentry$$00000004          0x08000258   Section        6  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    .ARM.Collect$$rtentry$$00000009          0x0800025e   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    .ARM.Collect$$rtentry$$0000000A          0x0800025e   Section        4  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    .ARM.Collect$$rtentry$$0000000C          0x08000262   Section        0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    .ARM.Collect$$rtentry$$0000000D          0x08000262   Section        8  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    .ARM.Collect$$rtexit$$00000000           0x0800026a   Section        2  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    .ARM.Collect$$rtexit$$00000002           0x0800026c   Section        0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    .ARM.Collect$$rtexit$$00000003           0x0800026c   Section        4  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    .ARM.Collect$$rtexit$$00000004           0x08000270   Section        6  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    .text                                    0x08000278   Section        0  main.o(.text)
    .text                                    0x080003fc   Section        0  stm32f10x_it.o(.text)
    .text                                    0x08000418   Section        0  system_stm32f10x.o(.text)
    SetSysClockTo72                          0x08000419   Thumb Code   214  system_stm32f10x.o(.text)
    SetSysClock                              0x080004ef   Thumb Code     8  system_stm32f10x.o(.text)
    .text                                    0x080005f8   Section        0  delay.o(.text)
    .text                                    0x080006cc   Section        0  usart.o(.text)
    .text                                    0x0800080c   Section        0  camera.o(.text)
    .text                                    0x08000a60   Section       64  startup_stm32f10x_hd.o(.text)
    .text                                    0x08000aa0   Section        0  stm32f10x_gpio.o(.text)
    .text                                    0x08000dfc   Section        0  stm32f10x_rcc.o(.text)
    .text                                    0x080011a0   Section        0  stm32f10x_usart.o(.text)
    .text                                    0x080015a8   Section        0  misc.o(.text)
    .text                                    0x08001684   Section        2  use_no_semi_2.o(.text)
    .text                                    0x08001688   Section        0  vsprintf.o(.text)
    .text                                    0x080016ac   Section        0  __2printf.o(.text)
    .text                                    0x080016c4   Section        0  __printf_flags_ss_wp.o(.text)
    .text                                    0x0800184c   Section        0  heapauxi.o(.text)
    .text                                    0x08001852   Section        2  use_no_semi.o(.text)
    .text                                    0x08001854   Section        0  _printf_pad.o(.text)
    .text                                    0x080018a2   Section        0  _printf_truncate.o(.text)
    .text                                    0x080018c6   Section        0  _printf_str.o(.text)
    .text                                    0x08001918   Section        0  _printf_dec.o(.text)
    .text                                    0x08001990   Section        0  _printf_charcount.o(.text)
    .text                                    0x080019b8   Section        0  _printf_fp_dec.o(.text)
    _fp_digits                               0x080019bb   Thumb Code   432  _printf_fp_dec.o(.text)
    .text                                    0x08001dd8   Section        0  _printf_char_common.o(.text)
    _printf_input_char                       0x08001dd9   Thumb Code    10  _printf_char_common.o(.text)
    .text                                    0x08001e08   Section        0  _sputc.o(.text)
    .text                                    0x08001e14   Section        0  _printf_char_file.o(.text)
    .text                                    0x08001e38   Section        0  _printf_wctomb.o(.text)
    .text                                    0x08001ef4   Section        0  _printf_longlong_dec.o(.text)
    .text                                    0x08001f70   Section        0  _printf_oct_int_ll.o(.text)
    _printf_longlong_oct_internal            0x08001f71   Thumb Code     0  _printf_oct_int_ll.o(.text)
    .text                                    0x08001fe0   Section        0  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_common                       0x08001fe1   Thumb Code     0  _printf_hex_int_ll_ptr.o(.text)
    .text                                    0x08002074   Section        8  rt_locale_intlibspace.o(.text)
    .text                                    0x0800207c   Section      138  lludiv10.o(.text)
    .text                                    0x08002106   Section        0  _printf_intcommon.o(.text)
    .text                                    0x080021b8   Section        0  _printf_fp_hex.o(.text)
    .text                                    0x080024b4   Section        0  _printf_fp_infnan.o(.text)
    .text                                    0x08002534   Section        0  _printf_char.o(.text)
    .text                                    0x08002560   Section        0  _printf_wchar.o(.text)
    .text                                    0x0800258c   Section        0  bigflt0.o(.text)
    .text                                    0x08002670   Section        0  ferror.o(.text)
    .text                                    0x08002678   Section        0  _wcrtomb.o(.text)
    .text                                    0x080026b8   Section        8  libspace.o(.text)
    .text                                    0x080026c0   Section       74  sys_stackheap_outer.o(.text)
    .text                                    0x0800270c   Section       16  rt_ctype_table.o(.text)
    .text                                    0x0800271c   Section        0  exit.o(.text)
    .text                                    0x08002730   Section      128  strcmpv7m.o(.text)
    CL$$btod_d2e                             0x080027b0   Section       62  btod.o(CL$$btod_d2e)
    CL$$btod_d2e_denorm_low                  0x080027ee   Section       70  btod.o(CL$$btod_d2e_denorm_low)
    CL$$btod_d2e_norm_op1                    0x08002834   Section       96  btod.o(CL$$btod_d2e_norm_op1)
    CL$$btod_div_common                      0x08002894   Section      824  btod.o(CL$$btod_div_common)
    CL$$btod_e2e                             0x08002bcc   Section      220  btod.o(CL$$btod_e2e)
    CL$$btod_ediv                            0x08002ca8   Section       42  btod.o(CL$$btod_ediv)
    CL$$btod_emul                            0x08002cd2   Section       42  btod.o(CL$$btod_emul)
    CL$$btod_mult_common                     0x08002cfc   Section      580  btod.o(CL$$btod_mult_common)
    i.__ARM_fpclassify                       0x08002f40   Section        0  fpclassify.o(i.__ARM_fpclassify)
    i._is_digit                              0x08002f68   Section        0  __printf_wp.o(i._is_digit)
    locale$$code                             0x08002f78   Section       44  lc_numeric_c.o(locale$$code)
    locale$$code                             0x08002fa4   Section       44  lc_ctype_c.o(locale$$code)
    x$fpl$dretinf                            0x08002fd0   Section       12  dretinf.o(x$fpl$dretinf)
    x$fpl$f2d                                0x08002fdc   Section       86  f2d.o(x$fpl$f2d)
    x$fpl$fdiv                               0x08003034   Section      388  fdiv.o(x$fpl$fdiv)
    _fdiv1                                   0x08003035   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    x$fpl$fflt                               0x080031b8   Section       48  fflt_clz.o(x$fpl$fflt)
    x$fpl$fnaninf                            0x080031e8   Section      140  fnaninf.o(x$fpl$fnaninf)
    x$fpl$fretinf                            0x08003274   Section       10  fretinf.o(x$fpl$fretinf)
    x$fpl$printf1                            0x0800327e   Section        4  printf1.o(x$fpl$printf1)
    x$fpl$printf2                            0x08003282   Section        4  printf2.o(x$fpl$printf2)
    .constdata                               0x08003286   Section       17  __printf_flags_ss_wp.o(.constdata)
    x$fpl$usenofp                            0x08003286   Section        0  usenofp.o(x$fpl$usenofp)
    maptable                                 0x08003286   Data          17  __printf_flags_ss_wp.o(.constdata)
    .constdata                               0x08003298   Section        8  _printf_wctomb.o(.constdata)
    initial_mbstate                          0x08003298   Data           8  _printf_wctomb.o(.constdata)
    .constdata                               0x080032a0   Section       40  _printf_hex_int_ll_ptr.o(.constdata)
    uc_hextab                                0x080032a0   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    lc_hextab                                0x080032b4   Data          20  _printf_hex_int_ll_ptr.o(.constdata)
    .constdata                               0x080032c8   Section       38  _printf_fp_hex.o(.constdata)
    lc_hextab                                0x080032c8   Data          19  _printf_fp_hex.o(.constdata)
    uc_hextab                                0x080032db   Data          19  _printf_fp_hex.o(.constdata)
    .constdata                               0x080032f0   Section      148  bigflt0.o(.constdata)
    tenpwrs_x                                0x080032f0   Data          60  bigflt0.o(.constdata)
    tenpwrs_i                                0x0800332c   Data          64  bigflt0.o(.constdata)
    locale$$data                             0x080033a4   Section       28  lc_numeric_c.o(locale$$data)
    __lcnum_c_name                           0x080033a8   Data           2  lc_numeric_c.o(locale$$data)
    __lcnum_c_start                          0x080033b0   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_point                          0x080033bc   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_thousands                      0x080033be   Data           0  lc_numeric_c.o(locale$$data)
    __lcnum_c_grouping                       0x080033bf   Data           0  lc_numeric_c.o(locale$$data)
    locale$$data                             0x080033c0   Section      272  lc_ctype_c.o(locale$$data)
    __lcnum_c_end                            0x080033c0   Data           0  lc_numeric_c.o(locale$$data)
    __lcctype_c_name                         0x080033c4   Data           2  lc_ctype_c.o(locale$$data)
    __lcctype_c_start                        0x080033cc   Data           0  lc_ctype_c.o(locale$$data)
    __lcctype_c_end                          0x080034d0   Data           0  lc_ctype_c.o(locale$$data)
    .data                                    0x20000000   Section       28  main.o(.data)
    .data                                    0x2000001c   Section       20  system_stm32f10x.o(.data)
    .data                                    0x20000030   Section        4  delay.o(.data)
    fac_us                                   0x20000030   Data           1  delay.o(.data)
    fac_ms                                   0x20000032   Data           2  delay.o(.data)
    .data                                    0x20000034   Section        6  usart.o(.data)
    .data                                    0x2000003a   Section       10  camera.o(.data)
    RxState                                  0x20000042   Data           1  camera.o(.data)
    pRxPacket                                0x20000043   Data           1  camera.o(.data)
    .data                                    0x20000044   Section       20  stm32f10x_rcc.o(.data)
    APBAHBPrescTable                         0x20000044   Data          16  stm32f10x_rcc.o(.data)
    ADCPrescTable                            0x20000054   Data           4  stm32f10x_rcc.o(.data)
    .bss                                     0x20000058   Section      200  usart.o(.bss)
    .bss                                     0x20000120   Section       96  libspace.o(.bss)
    HEAP                                     0x20000180   Section      512  startup_stm32f10x_hd.o(HEAP)
    Heap_Mem                                 0x20000180   Data         512  startup_stm32f10x_hd.o(HEAP)
    STACK                                    0x20000380   Section     1024  startup_stm32f10x_hd.o(STACK)
    Stack_Mem                                0x20000380   Data        1024  startup_stm32f10x_hd.o(STACK)
    __initial_sp                             0x20000780   Data           0  startup_stm32f10x_hd.o(STACK)

    Global Symbols

    Symbol Name                              Value     Ov Type        Size  Object(Section)

    BuildAttributes$$THM_ISAv4$P$D$K$B$S$PE$A:L22UL41UL21$X:L11$S22US41US21$IEEE1$IW$USESV6$~STKCKD$USESV7$~SHL$OTIME$ROPI$IEEEJ$EBA8$UX$STANDARDLIB$REQ8$PRES8$EABIv2 0x00000000   Number         0  anon$$obj.o ABSOLUTE
    __ARM_use_no_argv                        0x00000000   Number         0  main.o ABSOLUTE
    _printf_flags                            0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_return_value                     0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_sizespec                         0x00000000   Number         0  printf_stubs.o ABSOLUTE
    _printf_widthprec                        0x00000000   Number         0  printf_stubs.o ABSOLUTE
    __ARM_exceptions_init                     - Undefined Weak Reference
    __alloca_initialize                       - Undefined Weak Reference
    __arm_fini_                               - Undefined Weak Reference
    __arm_preinit_                            - Undefined Weak Reference
    __cpp_initialize__aeabi_                  - Undefined Weak Reference
    __cxa_finalize                            - Undefined Weak Reference
    __sigvec_lookup                           - Undefined Weak Reference
    _atexit_init                              - Undefined Weak Reference
    _call_atexit_fns                          - Undefined Weak Reference
    _clock_init                               - Undefined Weak Reference
    _fp_trap_init                             - Undefined Weak Reference
    _fp_trap_shutdown                         - Undefined Weak Reference
    _get_lc_collate                           - Undefined Weak Reference
    _get_lc_monetary                          - Undefined Weak Reference
    _get_lc_time                              - Undefined Weak Reference
    _getenv_init                              - Undefined Weak Reference
    _handle_redirection                       - Undefined Weak Reference
    _init_alloc                               - Undefined Weak Reference
    _init_user_alloc                          - Undefined Weak Reference
    _initio                                   - Undefined Weak Reference
    _mutex_acquire                            - Undefined Weak Reference
    _mutex_release                            - Undefined Weak Reference
    _printf_mbtowc                            - Undefined Weak Reference
    _printf_wc                                - Undefined Weak Reference
    _rand_init                                - Undefined Weak Reference
    _signal_finish                            - Undefined Weak Reference
    _signal_init                              - Undefined Weak Reference
    _terminate_alloc                          - Undefined Weak Reference
    _terminate_user_alloc                     - Undefined Weak Reference
    _terminateio                              - Undefined Weak Reference
    __Vectors_Size                           0x00000130   Number         0  startup_stm32f10x_hd.o ABSOLUTE
    __Vectors                                0x08000000   Data           4  startup_stm32f10x_hd.o(RESET)
    __Vectors_End                            0x08000130   Data           0  startup_stm32f10x_hd.o(RESET)
    __main                                   0x08000131   Thumb Code     8  __main.o(!!!main)
    __scatterload                            0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_rt2                        0x08000139   Thumb Code    44  __scatter.o(!!!scatter)
    __scatterload_rt2_thumb_only             0x08000139   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_null                       0x08000147   Thumb Code     0  __scatter.o(!!!scatter)
    __scatterload_copy                       0x0800016d   Thumb Code    26  __scatter_copy.o(!!handler_copy)
    __scatterload_zeroinit                   0x08000189   Thumb Code    28  __scatter_zi.o(!!handler_zi)
    _printf_n                                0x080001a5   Thumb Code     0  _printf_n.o(.ARM.Collect$$_printf_percent$$00000001)
    _printf_percent                          0x080001a5   Thumb Code     0  _printf_percent.o(.ARM.Collect$$_printf_percent$$00000000)
    _printf_p                                0x080001ab   Thumb Code     0  _printf_p.o(.ARM.Collect$$_printf_percent$$00000002)
    _printf_f                                0x080001b1   Thumb Code     0  _printf_f.o(.ARM.Collect$$_printf_percent$$00000003)
    _printf_e                                0x080001b7   Thumb Code     0  _printf_e.o(.ARM.Collect$$_printf_percent$$00000004)
    _printf_g                                0x080001bd   Thumb Code     0  _printf_g.o(.ARM.Collect$$_printf_percent$$00000005)
    _printf_a                                0x080001c3   Thumb Code     0  _printf_a.o(.ARM.Collect$$_printf_percent$$00000006)
    _printf_ll                               0x080001c9   Thumb Code     0  _printf_ll.o(.ARM.Collect$$_printf_percent$$00000007)
    _printf_i                                0x080001d3   Thumb Code     0  _printf_i.o(.ARM.Collect$$_printf_percent$$00000008)
    _printf_d                                0x080001d9   Thumb Code     0  _printf_d.o(.ARM.Collect$$_printf_percent$$00000009)
    _printf_u                                0x080001df   Thumb Code     0  _printf_u.o(.ARM.Collect$$_printf_percent$$0000000A)
    _printf_o                                0x080001e5   Thumb Code     0  _printf_o.o(.ARM.Collect$$_printf_percent$$0000000B)
    _printf_x                                0x080001eb   Thumb Code     0  _printf_x.o(.ARM.Collect$$_printf_percent$$0000000C)
    _printf_lli                              0x080001f1   Thumb Code     0  _printf_lli.o(.ARM.Collect$$_printf_percent$$0000000D)
    _printf_lld                              0x080001f7   Thumb Code     0  _printf_lld.o(.ARM.Collect$$_printf_percent$$0000000E)
    _printf_llu                              0x080001fd   Thumb Code     0  _printf_llu.o(.ARM.Collect$$_printf_percent$$0000000F)
    _printf_llo                              0x08000203   Thumb Code     0  _printf_llo.o(.ARM.Collect$$_printf_percent$$00000010)
    _printf_llx                              0x08000209   Thumb Code     0  _printf_llx.o(.ARM.Collect$$_printf_percent$$00000011)
    _printf_l                                0x0800020f   Thumb Code     0  _printf_l.o(.ARM.Collect$$_printf_percent$$00000012)
    _printf_c                                0x08000219   Thumb Code     0  _printf_c.o(.ARM.Collect$$_printf_percent$$00000013)
    _printf_s                                0x0800021f   Thumb Code     0  _printf_s.o(.ARM.Collect$$_printf_percent$$00000014)
    _printf_lc                               0x08000225   Thumb Code     0  _printf_lc.o(.ARM.Collect$$_printf_percent$$00000015)
    _printf_ls                               0x0800022b   Thumb Code     0  _printf_ls.o(.ARM.Collect$$_printf_percent$$00000016)
    _printf_percent_end                      0x08000231   Thumb Code     0  _printf_percent_end.o(.ARM.Collect$$_printf_percent$$00000017)
    __rt_lib_init                            0x08000235   Thumb Code     0  libinit.o(.ARM.Collect$$libinit$$00000000)
    __rt_lib_init_fp_1                       0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000002)
    __rt_lib_init_heap_1                     0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000A)
    __rt_lib_init_lc_common                  0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000F)
    __rt_lib_init_preinit_1                  0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000004)
    __rt_lib_init_rand_1                     0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000E)
    __rt_lib_init_user_alloc_1               0x08000237   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000000C)
    __rt_lib_init_lc_collate_1               0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000011)
    __rt_lib_init_lc_ctype_2                 0x0800023d   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000012)
    __rt_lib_init_lc_ctype_1                 0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000013)
    __rt_lib_init_lc_monetary_1              0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000015)
    __rt_lib_init_lc_numeric_2               0x08000249   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000016)
    __rt_lib_init_alloca_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002E)
    __rt_lib_init_argv_1                     0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000002C)
    __rt_lib_init_atexit_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001B)
    __rt_lib_init_clock_1                    0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000021)
    __rt_lib_init_cpp_1                      0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000032)
    __rt_lib_init_exceptions_1               0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000030)
    __rt_lib_init_fp_trap_1                  0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001F)
    __rt_lib_init_getenv_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000023)
    __rt_lib_init_lc_numeric_1               0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000017)
    __rt_lib_init_lc_time_1                  0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000019)
    __rt_lib_init_return                     0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000033)
    __rt_lib_init_signal_1                   0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$0000001D)
    __rt_lib_init_stdio_1                    0x08000253   Thumb Code     0  libinit2.o(.ARM.Collect$$libinit$$00000025)
    __rt_lib_shutdown                        0x08000255   Thumb Code     0  libshutdown.o(.ARM.Collect$$libshutdown$$00000000)
    __rt_lib_shutdown_cpp_1                  0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000004)
    __rt_lib_shutdown_fini_1                 0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000002)
    __rt_lib_shutdown_fp_trap_1              0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000009)
    __rt_lib_shutdown_heap_1                 0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000011)
    __rt_lib_shutdown_return                 0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000012)
    __rt_lib_shutdown_signal_1               0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000C)
    __rt_lib_shutdown_stdio_1                0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$00000006)
    __rt_lib_shutdown_user_alloc_1           0x08000257   Thumb Code     0  libshutdown2.o(.ARM.Collect$$libshutdown$$0000000E)
    __rt_entry                               0x08000259   Thumb Code     0  __rtentry.o(.ARM.Collect$$rtentry$$00000000)
    __rt_entry_presh_1                       0x08000259   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000002)
    __rt_entry_sh                            0x08000259   Thumb Code     0  __rtentry4.o(.ARM.Collect$$rtentry$$00000004)
    __rt_entry_li                            0x0800025f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000A)
    __rt_entry_postsh_1                      0x0800025f   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$00000009)
    __rt_entry_main                          0x08000263   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000D)
    __rt_entry_postli_1                      0x08000263   Thumb Code     0  __rtentry2.o(.ARM.Collect$$rtentry$$0000000C)
    __rt_exit                                0x0800026b   Thumb Code     0  rtexit.o(.ARM.Collect$$rtexit$$00000000)
    __rt_exit_ls                             0x0800026d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000003)
    __rt_exit_prels_1                        0x0800026d   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000002)
    __rt_exit_exit                           0x08000271   Thumb Code     0  rtexit2.o(.ARM.Collect$$rtexit$$00000004)
    main                                     0x08000279   Thumb Code   186  main.o(.text)
    NMI_Handler                              0x080003fd   Thumb Code     2  stm32f10x_it.o(.text)
    HardFault_Handler                        0x080003ff   Thumb Code     4  stm32f10x_it.o(.text)
    MemManage_Handler                        0x08000403   Thumb Code     4  stm32f10x_it.o(.text)
    BusFault_Handler                         0x08000407   Thumb Code     4  stm32f10x_it.o(.text)
    UsageFault_Handler                       0x0800040b   Thumb Code     4  stm32f10x_it.o(.text)
    SVC_Handler                              0x0800040f   Thumb Code     2  stm32f10x_it.o(.text)
    DebugMon_Handler                         0x08000411   Thumb Code     2  stm32f10x_it.o(.text)
    PendSV_Handler                           0x08000413   Thumb Code     2  stm32f10x_it.o(.text)
    SysTick_Handler                          0x08000415   Thumb Code     2  stm32f10x_it.o(.text)
    SystemInit                               0x080004f7   Thumb Code    78  system_stm32f10x.o(.text)
    SystemCoreClockUpdate                    0x08000545   Thumb Code   142  system_stm32f10x.o(.text)
    delay_init                               0x080005f9   Thumb Code    50  delay.o(.text)
    delay_us                                 0x0800062b   Thumb Code    72  delay.o(.text)
    delay_ms                                 0x08000673   Thumb Code    72  delay.o(.text)
    _sys_exit                                0x080006cd   Thumb Code     4  usart.o(.text)
    fputc                                    0x080006d1   Thumb Code    24  usart.o(.text)
    uart_init                                0x080006e9   Thumb Code   152  usart.o(.text)
    USART1_IRQHandler                        0x08000781   Thumb Code   122  usart.o(.text)
    Camera_Init                              0x0800080d   Thumb Code   170  camera.o(.text)
    Camera_SendByte                          0x080008b7   Thumb Code    28  camera.o(.text)
    Camera_SendArray                         0x080008d3   Thumb Code    26  camera.o(.text)
    Camera_SendString                        0x080008ed   Thumb Code    26  camera.o(.text)
    Camera_Pow                               0x08000907   Thumb Code    20  camera.o(.text)
    Camera_SendNumber                        0x0800091b   Thumb Code    58  camera.o(.text)
    Camera_fputc                             0x08000955   Thumb Code    16  camera.o(.text)
    Camera_Printf                            0x08000965   Thumb Code    36  camera.o(.text)
    Camera_GetRxFlag                         0x08000989   Thumb Code    22  camera.o(.text)
    Camera_GetRxData                         0x0800099f   Thumb Code     6  camera.o(.text)
    USART2_IRQHandler                        0x080009a5   Thumb Code   160  camera.o(.text)
    Reset_Handler                            0x08000a61   Thumb Code     8  startup_stm32f10x_hd.o(.text)
    ADC1_2_IRQHandler                        0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    ADC3_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_RX1_IRQHandler                      0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    CAN1_SCE_IRQHandler                      0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel1_IRQHandler                 0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel2_IRQHandler                 0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel3_IRQHandler                 0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel4_IRQHandler                 0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel5_IRQHandler                 0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel6_IRQHandler                 0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA1_Channel7_IRQHandler                 0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel1_IRQHandler                 0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel2_IRQHandler                 0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel3_IRQHandler                 0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    DMA2_Channel4_5_IRQHandler               0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI0_IRQHandler                         0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI15_10_IRQHandler                     0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI1_IRQHandler                         0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI2_IRQHandler                         0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI3_IRQHandler                         0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI4_IRQHandler                         0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    EXTI9_5_IRQHandler                       0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FLASH_IRQHandler                         0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    FSMC_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_ER_IRQHandler                       0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C1_EV_IRQHandler                       0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_ER_IRQHandler                       0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    I2C2_EV_IRQHandler                       0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    PVD_IRQHandler                           0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RCC_IRQHandler                           0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTCAlarm_IRQHandler                      0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    RTC_IRQHandler                           0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SDIO_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI1_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI2_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    SPI3_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TAMPER_IRQHandler                        0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_BRK_IRQHandler                      0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_CC_IRQHandler                       0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_TRG_COM_IRQHandler                  0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM1_UP_IRQHandler                       0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM2_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM3_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM4_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM5_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM6_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM7_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_BRK_IRQHandler                      0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_CC_IRQHandler                       0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_TRG_COM_IRQHandler                  0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    TIM8_UP_IRQHandler                       0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART4_IRQHandler                         0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    UART5_IRQHandler                         0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USART3_IRQHandler                        0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USBWakeUp_IRQHandler                     0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_HP_CAN1_TX_IRQHandler                0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    USB_LP_CAN1_RX0_IRQHandler               0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    WWDG_IRQHandler                          0x08000a7b   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    __user_initial_stackheap                 0x08000a7d   Thumb Code     0  startup_stm32f10x_hd.o(.text)
    GPIO_DeInit                              0x08000aa1   Thumb Code   172  stm32f10x_gpio.o(.text)
    GPIO_AFIODeInit                          0x08000b4d   Thumb Code    20  stm32f10x_gpio.o(.text)
    GPIO_Init                                0x08000b61   Thumb Code   278  stm32f10x_gpio.o(.text)
    GPIO_StructInit                          0x08000c77   Thumb Code    16  stm32f10x_gpio.o(.text)
    GPIO_ReadInputDataBit                    0x08000c87   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadInputData                       0x08000c99   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputDataBit                   0x08000ca1   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_ReadOutputData                      0x08000cb3   Thumb Code     8  stm32f10x_gpio.o(.text)
    GPIO_SetBits                             0x08000cbb   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_ResetBits                           0x08000cbf   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_WriteBit                            0x08000cc3   Thumb Code    10  stm32f10x_gpio.o(.text)
    GPIO_Write                               0x08000ccd   Thumb Code     4  stm32f10x_gpio.o(.text)
    GPIO_PinLockConfig                       0x08000cd1   Thumb Code    18  stm32f10x_gpio.o(.text)
    GPIO_EventOutputConfig                   0x08000ce3   Thumb Code    26  stm32f10x_gpio.o(.text)
    GPIO_EventOutputCmd                      0x08000cfd   Thumb Code     6  stm32f10x_gpio.o(.text)
    GPIO_PinRemapConfig                      0x08000d03   Thumb Code   138  stm32f10x_gpio.o(.text)
    GPIO_EXTILineConfig                      0x08000d8d   Thumb Code    66  stm32f10x_gpio.o(.text)
    GPIO_ETH_MediaInterfaceConfig            0x08000dcf   Thumb Code     8  stm32f10x_gpio.o(.text)
    RCC_DeInit                               0x08000dfd   Thumb Code    64  stm32f10x_rcc.o(.text)
    RCC_HSEConfig                            0x08000e3d   Thumb Code    70  stm32f10x_rcc.o(.text)
    RCC_GetFlagStatus                        0x08000e83   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_WaitForHSEStartUp                    0x08000ebb   Thumb Code    56  stm32f10x_rcc.o(.text)
    RCC_AdjustHSICalibrationValue            0x08000ef3   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_HSICmd                               0x08000f07   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_PLLConfig                            0x08000f0d   Thumb Code    24  stm32f10x_rcc.o(.text)
    RCC_PLLCmd                               0x08000f25   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_SYSCLKConfig                         0x08000f2b   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_GetSYSCLKSource                      0x08000f3d   Thumb Code    10  stm32f10x_rcc.o(.text)
    RCC_HCLKConfig                           0x08000f47   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK1Config                          0x08000f59   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_PCLK2Config                          0x08000f6b   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ITConfig                             0x08000f7f   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_USBCLKConfig                         0x08000f99   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ADCCLKConfig                         0x08000fa1   Thumb Code    18  stm32f10x_rcc.o(.text)
    RCC_LSEConfig                            0x08000fb3   Thumb Code    50  stm32f10x_rcc.o(.text)
    RCC_LSICmd                               0x08000fe5   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_RTCCLKConfig                         0x08000feb   Thumb Code    12  stm32f10x_rcc.o(.text)
    RCC_RTCCLKCmd                            0x08000ff7   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_GetClocksFreq                        0x08000fff   Thumb Code   192  stm32f10x_rcc.o(.text)
    RCC_AHBPeriphClockCmd                    0x080010bf   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphClockCmd                   0x080010d9   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphClockCmd                   0x080010f3   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB2PeriphResetCmd                   0x0800110d   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_APB1PeriphResetCmd                   0x08001127   Thumb Code    26  stm32f10x_rcc.o(.text)
    RCC_BackupResetCmd                       0x08001141   Thumb Code     8  stm32f10x_rcc.o(.text)
    RCC_ClockSecuritySystemCmd               0x08001149   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_MCOConfig                            0x0800114f   Thumb Code     6  stm32f10x_rcc.o(.text)
    RCC_ClearFlag                            0x08001155   Thumb Code    14  stm32f10x_rcc.o(.text)
    RCC_GetITStatus                          0x08001163   Thumb Code    20  stm32f10x_rcc.o(.text)
    RCC_ClearITPendingBit                    0x08001177   Thumb Code     6  stm32f10x_rcc.o(.text)
    USART_DeInit                             0x080011a1   Thumb Code   134  stm32f10x_usart.o(.text)
    USART_Init                               0x08001227   Thumb Code   210  stm32f10x_usart.o(.text)
    USART_StructInit                         0x080012f9   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ClockInit                          0x08001311   Thumb Code    34  stm32f10x_usart.o(.text)
    USART_ClockStructInit                    0x08001333   Thumb Code    12  stm32f10x_usart.o(.text)
    USART_Cmd                                0x0800133f   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_ITConfig                           0x08001357   Thumb Code    74  stm32f10x_usart.o(.text)
    USART_DMACmd                             0x080013a1   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_SetAddress                         0x080013b3   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_WakeUpConfig                       0x080013c5   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_ReceiverWakeUpCmd                  0x080013d7   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_LINBreakDetectLengthConfig         0x080013ef   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_LINCmd                             0x08001401   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SendData                           0x08001419   Thumb Code     8  stm32f10x_usart.o(.text)
    USART_ReceiveData                        0x08001421   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SendBreak                          0x0800142b   Thumb Code    10  stm32f10x_usart.o(.text)
    USART_SetGuardTime                       0x08001435   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SetPrescaler                       0x08001445   Thumb Code    16  stm32f10x_usart.o(.text)
    USART_SmartCardCmd                       0x08001455   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_SmartCardNACKCmd                   0x0800146d   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_HalfDuplexCmd                      0x08001485   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_OverSampling8Cmd                   0x0800149d   Thumb Code    22  stm32f10x_usart.o(.text)
    USART_OneBitMethodCmd                    0x080014b3   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_IrDAConfig                         0x080014cb   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_IrDACmd                            0x080014dd   Thumb Code    24  stm32f10x_usart.o(.text)
    USART_GetFlagStatus                      0x080014f5   Thumb Code    26  stm32f10x_usart.o(.text)
    USART_ClearFlag                          0x0800150f   Thumb Code    18  stm32f10x_usart.o(.text)
    USART_GetITStatus                        0x08001521   Thumb Code    84  stm32f10x_usart.o(.text)
    USART_ClearITPendingBit                  0x08001575   Thumb Code    52  stm32f10x_usart.o(.text)
    NVIC_PriorityGroupConfig                 0x080015a9   Thumb Code    10  misc.o(.text)
    NVIC_Init                                0x080015b3   Thumb Code   100  misc.o(.text)
    NVIC_SetVectorTable                      0x08001617   Thumb Code    14  misc.o(.text)
    NVIC_SystemLPConfig                      0x08001625   Thumb Code    34  misc.o(.text)
    SysTick_CLKSourceConfig                  0x08001647   Thumb Code    40  misc.o(.text)
    __use_no_semihosting                     0x08001685   Thumb Code     2  use_no_semi_2.o(.text)
    vsprintf                                 0x08001689   Thumb Code    32  vsprintf.o(.text)
    __2printf                                0x080016ad   Thumb Code    20  __2printf.o(.text)
    __printf                                 0x080016c5   Thumb Code   388  __printf_flags_ss_wp.o(.text)
    __use_two_region_memory                  0x0800184d   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_escrow$2region                 0x0800184f   Thumb Code     2  heapauxi.o(.text)
    __rt_heap_expand$2region                 0x08001851   Thumb Code     2  heapauxi.o(.text)
    __I$use$semihosting                      0x08001853   Thumb Code     0  use_no_semi.o(.text)
    __use_no_semihosting_swi                 0x08001853   Thumb Code     2  use_no_semi.o(.text)
    _printf_pre_padding                      0x08001855   Thumb Code    44  _printf_pad.o(.text)
    _printf_post_padding                     0x08001881   Thumb Code    34  _printf_pad.o(.text)
    _printf_truncate_signed                  0x080018a3   Thumb Code    18  _printf_truncate.o(.text)
    _printf_truncate_unsigned                0x080018b5   Thumb Code    18  _printf_truncate.o(.text)
    _printf_str                              0x080018c7   Thumb Code    82  _printf_str.o(.text)
    _printf_int_dec                          0x08001919   Thumb Code   104  _printf_dec.o(.text)
    _printf_charcount                        0x08001991   Thumb Code    40  _printf_charcount.o(.text)
    __lib_sel_fp_printf                      0x080019b9   Thumb Code     2  _printf_fp_dec.o(.text)
    _printf_fp_dec_real                      0x08001b6b   Thumb Code   620  _printf_fp_dec.o(.text)
    _printf_char_common                      0x08001de3   Thumb Code    32  _printf_char_common.o(.text)
    _sputc                                   0x08001e09   Thumb Code    10  _sputc.o(.text)
    _printf_char_file                        0x08001e15   Thumb Code    32  _printf_char_file.o(.text)
    _printf_wctomb                           0x08001e39   Thumb Code   182  _printf_wctomb.o(.text)
    _printf_longlong_dec                     0x08001ef5   Thumb Code   108  _printf_longlong_dec.o(.text)
    _printf_longlong_oct                     0x08001f71   Thumb Code    66  _printf_oct_int_ll.o(.text)
    _printf_int_oct                          0x08001fb3   Thumb Code    24  _printf_oct_int_ll.o(.text)
    _printf_ll_oct                           0x08001fcb   Thumb Code    12  _printf_oct_int_ll.o(.text)
    _printf_longlong_hex                     0x08001fe1   Thumb Code    86  _printf_hex_int_ll_ptr.o(.text)
    _printf_int_hex                          0x08002037   Thumb Code    28  _printf_hex_int_ll_ptr.o(.text)
    _printf_ll_hex                           0x08002053   Thumb Code    12  _printf_hex_int_ll_ptr.o(.text)
    _printf_hex_ptr                          0x0800205f   Thumb Code    18  _printf_hex_int_ll_ptr.o(.text)
    __rt_locale                              0x08002075   Thumb Code     8  rt_locale_intlibspace.o(.text)
    _ll_udiv10                               0x0800207d   Thumb Code   138  lludiv10.o(.text)
    _printf_int_common                       0x08002107   Thumb Code   178  _printf_intcommon.o(.text)
    _printf_fp_hex_real                      0x080021b9   Thumb Code   756  _printf_fp_hex.o(.text)
    _printf_fp_infnan                        0x080024b5   Thumb Code   112  _printf_fp_infnan.o(.text)
    _printf_cs_common                        0x08002535   Thumb Code    20  _printf_char.o(.text)
    _printf_char                             0x08002549   Thumb Code    16  _printf_char.o(.text)
    _printf_string                           0x08002559   Thumb Code     8  _printf_char.o(.text)
    _printf_lcs_common                       0x08002561   Thumb Code    20  _printf_wchar.o(.text)
    _printf_wchar                            0x08002575   Thumb Code    16  _printf_wchar.o(.text)
    _printf_wstring                          0x08002585   Thumb Code     8  _printf_wchar.o(.text)
    _btod_etento                             0x0800258d   Thumb Code   224  bigflt0.o(.text)
    ferror                                   0x08002671   Thumb Code     8  ferror.o(.text)
    _wcrtomb                                 0x08002679   Thumb Code    64  _wcrtomb.o(.text)
    __user_libspace                          0x080026b9   Thumb Code     8  libspace.o(.text)
    __user_perproc_libspace                  0x080026b9   Thumb Code     0  libspace.o(.text)
    __user_perthread_libspace                0x080026b9   Thumb Code     0  libspace.o(.text)
    __user_setup_stackheap                   0x080026c1   Thumb Code    74  sys_stackheap_outer.o(.text)
    __rt_ctype_table                         0x0800270d   Thumb Code    16  rt_ctype_table.o(.text)
    exit                                     0x0800271d   Thumb Code    18  exit.o(.text)
    strcmp                                   0x08002731   Thumb Code   128  strcmpv7m.o(.text)
    _btod_d2e                                0x080027b1   Thumb Code    62  btod.o(CL$$btod_d2e)
    _d2e_denorm_low                          0x080027ef   Thumb Code    70  btod.o(CL$$btod_d2e_denorm_low)
    _d2e_norm_op1                            0x08002835   Thumb Code    96  btod.o(CL$$btod_d2e_norm_op1)
    __btod_div_common                        0x08002895   Thumb Code   696  btod.o(CL$$btod_div_common)
    _e2e                                     0x08002bcd   Thumb Code   220  btod.o(CL$$btod_e2e)
    _btod_ediv                               0x08002ca9   Thumb Code    42  btod.o(CL$$btod_ediv)
    _btod_emul                               0x08002cd3   Thumb Code    42  btod.o(CL$$btod_emul)
    __btod_mult_common                       0x08002cfd   Thumb Code   580  btod.o(CL$$btod_mult_common)
    __ARM_fpclassify                         0x08002f41   Thumb Code    40  fpclassify.o(i.__ARM_fpclassify)
    _is_digit                                0x08002f69   Thumb Code    14  __printf_wp.o(i._is_digit)
    _get_lc_numeric                          0x08002f79   Thumb Code    44  lc_numeric_c.o(locale$$code)
    _get_lc_ctype                            0x08002fa5   Thumb Code    44  lc_ctype_c.o(locale$$code)
    __fpl_dretinf                            0x08002fd1   Thumb Code    12  dretinf.o(x$fpl$dretinf)
    __aeabi_f2d                              0x08002fdd   Thumb Code     0  f2d.o(x$fpl$f2d)
    _f2d                                     0x08002fdd   Thumb Code    86  f2d.o(x$fpl$f2d)
    __aeabi_fdiv                             0x08003035   Thumb Code     0  fdiv.o(x$fpl$fdiv)
    _fdiv                                    0x08003035   Thumb Code   384  fdiv.o(x$fpl$fdiv)
    __aeabi_i2f                              0x080031b9   Thumb Code     0  fflt_clz.o(x$fpl$fflt)
    _fflt                                    0x080031b9   Thumb Code    48  fflt_clz.o(x$fpl$fflt)
    __fpl_fnaninf                            0x080031e9   Thumb Code   140  fnaninf.o(x$fpl$fnaninf)
    __fpl_fretinf                            0x08003275   Thumb Code    10  fretinf.o(x$fpl$fretinf)
    _printf_fp_dec                           0x0800327f   Thumb Code     4  printf1.o(x$fpl$printf1)
    _printf_fp_hex                           0x08003283   Thumb Code     4  printf2.o(x$fpl$printf2)
    __I$use$fp                               0x08003286   Number         0  usenofp.o(x$fpl$usenofp)
    Region$$Table$$Base                      0x08003384   Number         0  anon$$obj.o(Region$$Table)
    Region$$Table$$Limit                     0x080033a4   Number         0  anon$$obj.o(Region$$Table)
    __ctype                                  0x080033cd   Data           0  lc_ctype_c.o(locale$$data)
    D                                        0x20000000   Data           4  main.o(.data)
    x                                        0x20000004   Data           4  main.o(.data)
    Is                                       0x20000008   Data           4  main.o(.data)
    P                                        0x2000000c   Data           4  main.o(.data)
    Pm                                       0x20000010   Data           4  main.o(.data)
    xx                                       0x20000014   Data           4  main.o(.data)
    xy                                       0x20000018   Data           4  main.o(.data)
    SystemCoreClock                          0x2000001c   Data           4  system_stm32f10x.o(.data)
    AHBPrescTable                            0x20000020   Data          16  system_stm32f10x.o(.data)
    __stdout                                 0x20000034   Data           4  usart.o(.data)
    USART_RX_STA                             0x20000038   Data           2  usart.o(.data)
    Camera_RxPacket                          0x2000003a   Data           6  camera.o(.data)
    Camera_RxData                            0x20000040   Data           1  camera.o(.data)
    Camera_RxFlag                            0x20000041   Data           1  camera.o(.data)
    USART_RX_BUF                             0x20000058   Data         200  usart.o(.bss)
    __libspace_start                         0x20000120   Data          96  libspace.o(.bss)
    __temporary_stack_top$libspace           0x20000180   Data           0  libspace.o(.bss)



==============================================================================

Memory Map of the image

  Image Entry point : 0x08000131

  Load Region LR_IROM1 (Base: 0x08000000, Size: 0x00003528, Max: 0x00010000, ABSOLUTE)

    Execution Region ER_IROM1 (Exec base: 0x08000000, Load base: 0x08000000, Size: 0x000034d0, Max: 0x00010000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x08000000   0x08000000   0x00000130   Data   RO          305    RESET               startup_stm32f10x_hd.o
    0x08000130   0x08000130   0x00000008   Code   RO          396  * !!!main             c_w.l(__main.o)
    0x08000138   0x08000138   0x00000034   Code   RO          717    !!!scatter          c_w.l(__scatter.o)
    0x0800016c   0x0800016c   0x0000001a   Code   RO          719    !!handler_copy      c_w.l(__scatter_copy.o)
    0x08000186   0x08000186   0x00000002   PAD
    0x08000188   0x08000188   0x0000001c   Code   RO          721    !!handler_zi        c_w.l(__scatter_zi.o)
    0x080001a4   0x080001a4   0x00000000   Code   RO          393    .ARM.Collect$$_printf_percent$$00000000  c_w.l(_printf_percent.o)
    0x080001a4   0x080001a4   0x00000006   Code   RO          467    .ARM.Collect$$_printf_percent$$00000001  c_w.l(_printf_n.o)
    0x080001aa   0x080001aa   0x00000006   Code   RO          469    .ARM.Collect$$_printf_percent$$00000002  c_w.l(_printf_p.o)
    0x080001b0   0x080001b0   0x00000006   Code   RO          392    .ARM.Collect$$_printf_percent$$00000003  c_w.l(_printf_f.o)
    0x080001b6   0x080001b6   0x00000006   Code   RO          474    .ARM.Collect$$_printf_percent$$00000004  c_w.l(_printf_e.o)
    0x080001bc   0x080001bc   0x00000006   Code   RO          475    .ARM.Collect$$_printf_percent$$00000005  c_w.l(_printf_g.o)
    0x080001c2   0x080001c2   0x00000006   Code   RO          476    .ARM.Collect$$_printf_percent$$00000006  c_w.l(_printf_a.o)
    0x080001c8   0x080001c8   0x0000000a   Code   RO          481    .ARM.Collect$$_printf_percent$$00000007  c_w.l(_printf_ll.o)
    0x080001d2   0x080001d2   0x00000006   Code   RO          471    .ARM.Collect$$_printf_percent$$00000008  c_w.l(_printf_i.o)
    0x080001d8   0x080001d8   0x00000006   Code   RO          472    .ARM.Collect$$_printf_percent$$00000009  c_w.l(_printf_d.o)
    0x080001de   0x080001de   0x00000006   Code   RO          473    .ARM.Collect$$_printf_percent$$0000000A  c_w.l(_printf_u.o)
    0x080001e4   0x080001e4   0x00000006   Code   RO          470    .ARM.Collect$$_printf_percent$$0000000B  c_w.l(_printf_o.o)
    0x080001ea   0x080001ea   0x00000006   Code   RO          468    .ARM.Collect$$_printf_percent$$0000000C  c_w.l(_printf_x.o)
    0x080001f0   0x080001f0   0x00000006   Code   RO          478    .ARM.Collect$$_printf_percent$$0000000D  c_w.l(_printf_lli.o)
    0x080001f6   0x080001f6   0x00000006   Code   RO          479    .ARM.Collect$$_printf_percent$$0000000E  c_w.l(_printf_lld.o)
    0x080001fc   0x080001fc   0x00000006   Code   RO          480    .ARM.Collect$$_printf_percent$$0000000F  c_w.l(_printf_llu.o)
    0x08000202   0x08000202   0x00000006   Code   RO          485    .ARM.Collect$$_printf_percent$$00000010  c_w.l(_printf_llo.o)
    0x08000208   0x08000208   0x00000006   Code   RO          486    .ARM.Collect$$_printf_percent$$00000011  c_w.l(_printf_llx.o)
    0x0800020e   0x0800020e   0x0000000a   Code   RO          482    .ARM.Collect$$_printf_percent$$00000012  c_w.l(_printf_l.o)
    0x08000218   0x08000218   0x00000006   Code   RO          465    .ARM.Collect$$_printf_percent$$00000013  c_w.l(_printf_c.o)
    0x0800021e   0x0800021e   0x00000006   Code   RO          466    .ARM.Collect$$_printf_percent$$00000014  c_w.l(_printf_s.o)
    0x08000224   0x08000224   0x00000006   Code   RO          483    .ARM.Collect$$_printf_percent$$00000015  c_w.l(_printf_lc.o)
    0x0800022a   0x0800022a   0x00000006   Code   RO          484    .ARM.Collect$$_printf_percent$$00000016  c_w.l(_printf_ls.o)
    0x08000230   0x08000230   0x00000004   Code   RO          477    .ARM.Collect$$_printf_percent$$00000017  c_w.l(_printf_percent_end.o)
    0x08000234   0x08000234   0x00000002   Code   RO          589    .ARM.Collect$$libinit$$00000000  c_w.l(libinit.o)
    0x08000236   0x08000236   0x00000000   Code   RO          591    .ARM.Collect$$libinit$$00000002  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          593    .ARM.Collect$$libinit$$00000004  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          596    .ARM.Collect$$libinit$$0000000A  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          598    .ARM.Collect$$libinit$$0000000C  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000000   Code   RO          600    .ARM.Collect$$libinit$$0000000E  c_w.l(libinit2.o)
    0x08000236   0x08000236   0x00000006   Code   RO          601    .ARM.Collect$$libinit$$0000000F  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x00000000   Code   RO          603    .ARM.Collect$$libinit$$00000011  c_w.l(libinit2.o)
    0x0800023c   0x0800023c   0x0000000c   Code   RO          604    .ARM.Collect$$libinit$$00000012  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO          605    .ARM.Collect$$libinit$$00000013  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x00000000   Code   RO          607    .ARM.Collect$$libinit$$00000015  c_w.l(libinit2.o)
    0x08000248   0x08000248   0x0000000a   Code   RO          608    .ARM.Collect$$libinit$$00000016  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          609    .ARM.Collect$$libinit$$00000017  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          611    .ARM.Collect$$libinit$$00000019  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          613    .ARM.Collect$$libinit$$0000001B  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          615    .ARM.Collect$$libinit$$0000001D  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          617    .ARM.Collect$$libinit$$0000001F  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          619    .ARM.Collect$$libinit$$00000021  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          621    .ARM.Collect$$libinit$$00000023  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          623    .ARM.Collect$$libinit$$00000025  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          627    .ARM.Collect$$libinit$$0000002C  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          629    .ARM.Collect$$libinit$$0000002E  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          631    .ARM.Collect$$libinit$$00000030  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000000   Code   RO          633    .ARM.Collect$$libinit$$00000032  c_w.l(libinit2.o)
    0x08000252   0x08000252   0x00000002   Code   RO          634    .ARM.Collect$$libinit$$00000033  c_w.l(libinit2.o)
    0x08000254   0x08000254   0x00000002   Code   RO          665    .ARM.Collect$$libshutdown$$00000000  c_w.l(libshutdown.o)
    0x08000256   0x08000256   0x00000000   Code   RO          674    .ARM.Collect$$libshutdown$$00000002  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO          676    .ARM.Collect$$libshutdown$$00000004  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO          678    .ARM.Collect$$libshutdown$$00000006  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO          681    .ARM.Collect$$libshutdown$$00000009  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO          684    .ARM.Collect$$libshutdown$$0000000C  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO          686    .ARM.Collect$$libshutdown$$0000000E  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000000   Code   RO          689    .ARM.Collect$$libshutdown$$00000011  c_w.l(libshutdown2.o)
    0x08000256   0x08000256   0x00000002   Code   RO          690    .ARM.Collect$$libshutdown$$00000012  c_w.l(libshutdown2.o)
    0x08000258   0x08000258   0x00000000   Code   RO          414    .ARM.Collect$$rtentry$$00000000  c_w.l(__rtentry.o)
    0x08000258   0x08000258   0x00000000   Code   RO          501    .ARM.Collect$$rtentry$$00000002  c_w.l(__rtentry2.o)
    0x08000258   0x08000258   0x00000006   Code   RO          513    .ARM.Collect$$rtentry$$00000004  c_w.l(__rtentry4.o)
    0x0800025e   0x0800025e   0x00000000   Code   RO          503    .ARM.Collect$$rtentry$$00000009  c_w.l(__rtentry2.o)
    0x0800025e   0x0800025e   0x00000004   Code   RO          504    .ARM.Collect$$rtentry$$0000000A  c_w.l(__rtentry2.o)
    0x08000262   0x08000262   0x00000000   Code   RO          506    .ARM.Collect$$rtentry$$0000000C  c_w.l(__rtentry2.o)
    0x08000262   0x08000262   0x00000008   Code   RO          507    .ARM.Collect$$rtentry$$0000000D  c_w.l(__rtentry2.o)
    0x0800026a   0x0800026a   0x00000002   Code   RO          635    .ARM.Collect$$rtexit$$00000000  c_w.l(rtexit.o)
    0x0800026c   0x0800026c   0x00000000   Code   RO          645    .ARM.Collect$$rtexit$$00000002  c_w.l(rtexit2.o)
    0x0800026c   0x0800026c   0x00000004   Code   RO          646    .ARM.Collect$$rtexit$$00000003  c_w.l(rtexit2.o)
    0x08000270   0x08000270   0x00000006   Code   RO          647    .ARM.Collect$$rtexit$$00000004  c_w.l(rtexit2.o)
    0x08000276   0x08000276   0x00000002   PAD
    0x08000278   0x08000278   0x00000184   Code   RO            1    .text               main.o
    0x080003fc   0x080003fc   0x0000001a   Code   RO          122    .text               stm32f10x_it.o
    0x08000416   0x08000416   0x00000002   PAD
    0x08000418   0x08000418   0x000001e0   Code   RO          163    .text               system_stm32f10x.o
    0x080005f8   0x080005f8   0x000000d4   Code   RO          214    .text               delay.o
    0x080006cc   0x080006cc   0x00000140   Code   RO          245    .text               usart.o
    0x0800080c   0x0800080c   0x00000254   Code   RO          268    .text               camera.o
    0x08000a60   0x08000a60   0x00000040   Code   RO          306    .text               startup_stm32f10x_hd.o
    0x08000aa0   0x08000aa0   0x0000035c   Code   RO          310    .text               stm32f10x_gpio.o
    0x08000dfc   0x08000dfc   0x000003a4   Code   RO          322    .text               stm32f10x_rcc.o
    0x080011a0   0x080011a0   0x00000408   Code   RO          336    .text               stm32f10x_usart.o
    0x080015a8   0x080015a8   0x000000dc   Code   RO          348    .text               misc.o
    0x08001684   0x08001684   0x00000002   Code   RO          362    .text               c_w.l(use_no_semi_2.o)
    0x08001686   0x08001686   0x00000002   PAD
    0x08001688   0x08001688   0x00000024   Code   RO          364    .text               c_w.l(vsprintf.o)
    0x080016ac   0x080016ac   0x00000018   Code   RO          366    .text               c_w.l(__2printf.o)
    0x080016c4   0x080016c4   0x00000188   Code   RO          389    .text               c_w.l(__printf_flags_ss_wp.o)
    0x0800184c   0x0800184c   0x00000006   Code   RO          394    .text               c_w.l(heapauxi.o)
    0x08001852   0x08001852   0x00000002   Code   RO          412    .text               c_w.l(use_no_semi.o)
    0x08001854   0x08001854   0x0000004e   Code   RO          415    .text               c_w.l(_printf_pad.o)
    0x080018a2   0x080018a2   0x00000024   Code   RO          417    .text               c_w.l(_printf_truncate.o)
    0x080018c6   0x080018c6   0x00000052   Code   RO          419    .text               c_w.l(_printf_str.o)
    0x08001918   0x08001918   0x00000078   Code   RO          421    .text               c_w.l(_printf_dec.o)
    0x08001990   0x08001990   0x00000028   Code   RO          423    .text               c_w.l(_printf_charcount.o)
    0x080019b8   0x080019b8   0x0000041e   Code   RO          425    .text               c_w.l(_printf_fp_dec.o)
    0x08001dd6   0x08001dd6   0x00000002   PAD
    0x08001dd8   0x08001dd8   0x00000030   Code   RO          427    .text               c_w.l(_printf_char_common.o)
    0x08001e08   0x08001e08   0x0000000a   Code   RO          429    .text               c_w.l(_sputc.o)
    0x08001e12   0x08001e12   0x00000002   PAD
    0x08001e14   0x08001e14   0x00000024   Code   RO          431    .text               c_w.l(_printf_char_file.o)
    0x08001e38   0x08001e38   0x000000bc   Code   RO          433    .text               c_w.l(_printf_wctomb.o)
    0x08001ef4   0x08001ef4   0x0000007c   Code   RO          436    .text               c_w.l(_printf_longlong_dec.o)
    0x08001f70   0x08001f70   0x00000070   Code   RO          442    .text               c_w.l(_printf_oct_int_ll.o)
    0x08001fe0   0x08001fe0   0x00000094   Code   RO          462    .text               c_w.l(_printf_hex_int_ll_ptr.o)
    0x08002074   0x08002074   0x00000008   Code   RO          518    .text               c_w.l(rt_locale_intlibspace.o)
    0x0800207c   0x0800207c   0x0000008a   Code   RO          520    .text               c_w.l(lludiv10.o)
    0x08002106   0x08002106   0x000000b2   Code   RO          522    .text               c_w.l(_printf_intcommon.o)
    0x080021b8   0x080021b8   0x000002fc   Code   RO          524    .text               c_w.l(_printf_fp_hex.o)
    0x080024b4   0x080024b4   0x00000080   Code   RO          527    .text               c_w.l(_printf_fp_infnan.o)
    0x08002534   0x08002534   0x0000002c   Code   RO          531    .text               c_w.l(_printf_char.o)
    0x08002560   0x08002560   0x0000002c   Code   RO          533    .text               c_w.l(_printf_wchar.o)
    0x0800258c   0x0800258c   0x000000e4   Code   RO          535    .text               c_w.l(bigflt0.o)
    0x08002670   0x08002670   0x00000008   Code   RO          560    .text               c_w.l(ferror.o)
    0x08002678   0x08002678   0x00000040   Code   RO          564    .text               c_w.l(_wcrtomb.o)
    0x080026b8   0x080026b8   0x00000008   Code   RO          573    .text               c_w.l(libspace.o)
    0x080026c0   0x080026c0   0x0000004a   Code   RO          576    .text               c_w.l(sys_stackheap_outer.o)
    0x0800270a   0x0800270a   0x00000002   PAD
    0x0800270c   0x0800270c   0x00000010   Code   RO          578    .text               c_w.l(rt_ctype_table.o)
    0x0800271c   0x0800271c   0x00000012   Code   RO          580    .text               c_w.l(exit.o)
    0x0800272e   0x0800272e   0x00000002   PAD
    0x08002730   0x08002730   0x00000080   Code   RO          582    .text               c_w.l(strcmpv7m.o)
    0x080027b0   0x080027b0   0x0000003e   Code   RO          538    CL$$btod_d2e        c_w.l(btod.o)
    0x080027ee   0x080027ee   0x00000046   Code   RO          540    CL$$btod_d2e_denorm_low  c_w.l(btod.o)
    0x08002834   0x08002834   0x00000060   Code   RO          539    CL$$btod_d2e_norm_op1  c_w.l(btod.o)
    0x08002894   0x08002894   0x00000338   Code   RO          548    CL$$btod_div_common  c_w.l(btod.o)
    0x08002bcc   0x08002bcc   0x000000dc   Code   RO          545    CL$$btod_e2e        c_w.l(btod.o)
    0x08002ca8   0x08002ca8   0x0000002a   Code   RO          542    CL$$btod_ediv       c_w.l(btod.o)
    0x08002cd2   0x08002cd2   0x0000002a   Code   RO          541    CL$$btod_emul       c_w.l(btod.o)
    0x08002cfc   0x08002cfc   0x00000244   Code   RO          547    CL$$btod_mult_common  c_w.l(btod.o)
    0x08002f40   0x08002f40   0x00000028   Code   RO          571    i.__ARM_fpclassify  m_ws.l(fpclassify.o)
    0x08002f68   0x08002f68   0x0000000e   Code   RO          382    i._is_digit         c_w.l(__printf_wp.o)
    0x08002f76   0x08002f76   0x00000002   PAD
    0x08002f78   0x08002f78   0x0000002c   Code   RO          567    locale$$code        c_w.l(lc_numeric_c.o)
    0x08002fa4   0x08002fa4   0x0000002c   Code   RO          638    locale$$code        c_w.l(lc_ctype_c.o)
    0x08002fd0   0x08002fd0   0x0000000c   Code   RO          487    x$fpl$dretinf       fz_ws.l(dretinf.o)
    0x08002fdc   0x08002fdc   0x00000056   Code   RO          398    x$fpl$f2d           fz_ws.l(f2d.o)
    0x08003032   0x08003032   0x00000002   PAD
    0x08003034   0x08003034   0x00000184   Code   RO          401    x$fpl$fdiv          fz_ws.l(fdiv.o)
    0x080031b8   0x080031b8   0x00000030   Code   RO          405    x$fpl$fflt          fz_ws.l(fflt_clz.o)
    0x080031e8   0x080031e8   0x0000008c   Code   RO          489    x$fpl$fnaninf       fz_ws.l(fnaninf.o)
    0x08003274   0x08003274   0x0000000a   Code   RO          491    x$fpl$fretinf       fz_ws.l(fretinf.o)
    0x0800327e   0x0800327e   0x00000004   Code   RO          410    x$fpl$printf1       fz_ws.l(printf1.o)
    0x08003282   0x08003282   0x00000004   Code   RO          493    x$fpl$printf2       fz_ws.l(printf2.o)
    0x08003286   0x08003286   0x00000000   Code   RO          499    x$fpl$usenofp       fz_ws.l(usenofp.o)
    0x08003286   0x08003286   0x00000011   Data   RO          390    .constdata          c_w.l(__printf_flags_ss_wp.o)
    0x08003297   0x08003297   0x00000001   PAD
    0x08003298   0x08003298   0x00000008   Data   RO          434    .constdata          c_w.l(_printf_wctomb.o)
    0x080032a0   0x080032a0   0x00000028   Data   RO          463    .constdata          c_w.l(_printf_hex_int_ll_ptr.o)
    0x080032c8   0x080032c8   0x00000026   Data   RO          525    .constdata          c_w.l(_printf_fp_hex.o)
    0x080032ee   0x080032ee   0x00000002   PAD
    0x080032f0   0x080032f0   0x00000094   Data   RO          536    .constdata          c_w.l(bigflt0.o)
    0x08003384   0x08003384   0x00000020   Data   RO          715    Region$$Table       anon$$obj.o
    0x080033a4   0x080033a4   0x0000001c   Data   RO          566    locale$$data        c_w.l(lc_numeric_c.o)
    0x080033c0   0x080033c0   0x00000110   Data   RO          637    locale$$data        c_w.l(lc_ctype_c.o)


    Execution Region RW_IRAM1 (Exec base: 0x20000000, Load base: 0x080034d0, Size: 0x00000780, Max: 0x00005000, ABSOLUTE)

    Exec Addr    Load Addr    Size         Type   Attr      Idx    E Section Name        Object

    0x20000000   0x080034d0   0x0000001c   Data   RW            2    .data               main.o
    0x2000001c   0x080034ec   0x00000014   Data   RW          164    .data               system_stm32f10x.o
    0x20000030   0x08003500   0x00000004   Data   RW          215    .data               delay.o
    0x20000034   0x08003504   0x00000006   Data   RW          247    .data               usart.o
    0x2000003a   0x0800350a   0x0000000a   Data   RW          269    .data               camera.o
    0x20000044   0x08003514   0x00000014   Data   RW          323    .data               stm32f10x_rcc.o
    0x20000058        -       0x000000c8   Zero   RW          246    .bss                usart.o
    0x20000120        -       0x00000060   Zero   RW          574    .bss                c_w.l(libspace.o)
    0x20000180        -       0x00000200   Zero   RW          304    HEAP                startup_stm32f10x_hd.o
    0x20000380        -       0x00000400   Zero   RW          303    STACK               startup_stm32f10x_hd.o


==============================================================================

Image component sizes


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Object Name

       596         28          0         10          0       5555   camera.o
       212         18          0          4          0       1051   delay.o
       388        202          0         28          0     231855   main.o
       220         22          0          0          0       1809   misc.o
        64         26        304          0       1536        796   startup_stm32f10x_hd.o
       860         38          0          0          0       5741   stm32f10x_gpio.o
        26          0          0          0          0       1182   stm32f10x_it.o
       932         36          0         20          0       8920   stm32f10x_rcc.o
      1032         22          0          0          0       8464   stm32f10x_usart.o
         0          0          0          0          0         32   sys.o
       480         38          0         20          0       1751   system_stm32f10x.o
       320         18          0          6        200       3150   usart.o

    ----------------------------------------------------------------------
      5132        <USER>        <GROUP>         88       1736     270306   Object Totals
         0          0         32          0          0          0   (incl. Generated)
         2          0          0          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Member Name

        24          4          0          0          0         84   __2printf.o
         8          0          0          0          0         68   __main.o
       392          4         17          0          0         92   __printf_flags_ss_wp.o
        14          0          0          0          0         68   __printf_wp.o
         0          0          0          0          0          0   __rtentry.o
        12          0          0          0          0          0   __rtentry2.o
         6          0          0          0          0          0   __rtentry4.o
        52          8          0          0          0          0   __scatter.o
        26          0          0          0          0          0   __scatter_copy.o
        28          0          0          0          0          0   __scatter_zi.o
         6          0          0          0          0          0   _printf_a.o
         6          0          0          0          0          0   _printf_c.o
        44          0          0          0          0        108   _printf_char.o
        48          6          0          0          0         96   _printf_char_common.o
        36          4          0          0          0         80   _printf_char_file.o
        40          0          0          0          0         68   _printf_charcount.o
         6          0          0          0          0          0   _printf_d.o
       120         16          0          0          0         92   _printf_dec.o
         6          0          0          0          0          0   _printf_e.o
         6          0          0          0          0          0   _printf_f.o
      1054          0          0          0          0        216   _printf_fp_dec.o
       764          8         38          0          0        100   _printf_fp_hex.o
       128         16          0          0          0         84   _printf_fp_infnan.o
         6          0          0          0          0          0   _printf_g.o
       148          4         40          0          0        160   _printf_hex_int_ll_ptr.o
         6          0          0          0          0          0   _printf_i.o
       178          0          0          0          0         88   _printf_intcommon.o
        10          0          0          0          0          0   _printf_l.o
         6          0          0          0          0          0   _printf_lc.o
        10          0          0          0          0          0   _printf_ll.o
         6          0          0          0          0          0   _printf_lld.o
         6          0          0          0          0          0   _printf_lli.o
         6          0          0          0          0          0   _printf_llo.o
         6          0          0          0          0          0   _printf_llu.o
         6          0          0          0          0          0   _printf_llx.o
       124         16          0          0          0         92   _printf_longlong_dec.o
         6          0          0          0          0          0   _printf_ls.o
         6          0          0          0          0          0   _printf_n.o
         6          0          0          0          0          0   _printf_o.o
       112         10          0          0          0        124   _printf_oct_int_ll.o
         6          0          0          0          0          0   _printf_p.o
        78          0          0          0          0        108   _printf_pad.o
         0          0          0          0          0          0   _printf_percent.o
         4          0          0          0          0          0   _printf_percent_end.o
         6          0          0          0          0          0   _printf_s.o
        82          0          0          0          0         80   _printf_str.o
        36          0          0          0          0         84   _printf_truncate.o
         6          0          0          0          0          0   _printf_u.o
        44          0          0          0          0        108   _printf_wchar.o
       188          6          8          0          0         92   _printf_wctomb.o
         6          0          0          0          0          0   _printf_x.o
        10          0          0          0          0         68   _sputc.o
        64          0          0          0          0         92   _wcrtomb.o
       228          4        148          0          0         96   bigflt0.o
      1936        128          0          0          0        672   btod.o
        18          0          0          0          0         80   exit.o
         8          0          0          0          0         68   ferror.o
         6          0          0          0          0        152   heapauxi.o
        44         10        272          0          0         76   lc_ctype_c.o
        44         10         28          0          0         76   lc_numeric_c.o
         2          0          0          0          0          0   libinit.o
        30          0          0          0          0          0   libinit2.o
         2          0          0          0          0          0   libshutdown.o
         2          0          0          0          0          0   libshutdown2.o
         8          4          0          0         96         68   libspace.o
       138          0          0          0          0         80   lludiv10.o
        16          4          0          0          0         76   rt_ctype_table.o
         8          4          0          0          0         68   rt_locale_intlibspace.o
         2          0          0          0          0          0   rtexit.o
        10          0          0          0          0          0   rtexit2.o
       128          0          0          0          0         68   strcmpv7m.o
        74          0          0          0          0         80   sys_stackheap_outer.o
         2          0          0          0          0         68   use_no_semi.o
         2          0          0          0          0         68   use_no_semi_2.o
        36          4          0          0          0         76   vsprintf.o
        12          0          0          0          0         68   dretinf.o
        86          4          0          0          0         84   f2d.o
       388         76          0          0          0         96   fdiv.o
        48          0          0          0          0         68   fflt_clz.o
       140          4          0          0          0         84   fnaninf.o
        10          0          0          0          0         68   fretinf.o
         4          0          0          0          0         68   printf1.o
         4          0          0          0          0         68   printf2.o
         0          0          0          0          0          0   usenofp.o
        40          0          0          0          0         68   fpclassify.o

    ----------------------------------------------------------------------
      7498        <USER>        <GROUP>          0         96       4796   Library Totals
        18          0          3          0          0          0   (incl. Padding)

    ----------------------------------------------------------------------

      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   Library Name

      6748        270        551          0         96       4124   c_w.l
       692         84          0          0          0        604   fz_ws.l
        40          0          0          0          0         68   m_ws.l

    ----------------------------------------------------------------------
      7498        <USER>        <GROUP>          0         96       4796   Library Totals

    ----------------------------------------------------------------------

==============================================================================


      Code (inc. data)   RO Data    RW Data    ZI Data      Debug   

     12630        802        890         88       1832     271562   Grand Totals
     12630        802        890         88       1832     271562   ELF Image Totals
     12630        802        890         88          0          0   ROM Totals

==============================================================================

    Total RO  Size (Code + RO Data)                13520 (  13.20kB)
    Total RW  Size (RW Data + ZI Data)              1920 (   1.88kB)
    Total ROM Size (Code + RO Data + RW Data)      13608 (  13.29kB)

==============================================================================

