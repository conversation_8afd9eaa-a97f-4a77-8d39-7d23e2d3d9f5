#include "delay.h"
#include "sys.h"
#include "usart.h"
#include <Camera.h>
#include "matrix_keyboard_4x4.h"

float D, x, Is, P, Pm, xx, xy;

// 简单发送字符到MaixCAM (使用USART2)
void Send_Char_To_MaixCAM(char ch)
{
    // 等待发送完成
    while(!(USART2->SR & USART_SR_TXE));
    USART2->DR = ch;
}

// 初始化USART2用于MaixCAM通信
void MaixCAM_Simple_Init(void)
{
    GPIO_InitTypeDef GPIO_InitStructure;
    USART_InitTypeDef USART_InitStructure;

    // 使能时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_USART2, ENABLE);
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA, ENABLE);

    // 配置PA2为TX (复用推挽输出)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_2;
    GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_AF_PP;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置PA3为RX (浮空输入)
    GPIO_InitStructure.GPIO_Pin = GPIO_Pin_3;
    GPIO_InitStructure.GPIO_Mode = GPIO_Mode_IN_FLOATING;
    GPIO_Init(GPIOA, &GPIO_InitStructure);

    // 配置USART2
    USART_InitStructure.USART_BaudRate = 115200;
    USART_InitStructure.USART_WordLength = USART_WordLength_8b;
    USART_InitStructure.USART_StopBits = USART_StopBits_1;
    USART_InitStructure.USART_Parity = USART_Parity_No;
    USART_InitStructure.USART_HardwareFlowControl = USART_HardwareFlowControl_None;
    USART_InitStructure.USART_Mode = USART_Mode_Tx | USART_Mode_Rx;
    USART_Init(USART2, &USART_InitStructure);

    // 使能USART2
    USART_Cmd(USART2, ENABLE);
}

int main(void)
{
    delay_init(); // 延时函数初始化
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2); // 设置中断优先级分组2
    uart_init(9600); // 串口初始化为9600
	Camera_Init();

	// 初始化矩阵键盘和MaixCAM通信
	Matrix_Keyboard_Init();
	MaixCAM_Simple_Init();

	// 初始化数据
	D = 2011.1;
	x = 22.22;
	Is = 33.333;
	P = 44.444;
	Pm = 55.555;

	printf("STM32系统启动完成\r\n");
	printf("按键1发送1到MaixCAM显示\r\n");

    while (1)
	{
		// 扫描矩阵键盘
		uint8_t key = Matrix_Keyboard_GetKey();
		if(key != KEY_VALUE_NONE)
		{
			char key_char = Matrix_Keyboard_GetChar(key);
			printf("按键: %c, 发送到MaixCAM\r\n", key_char);

			// 直接发送字符到MaixCAM
			Send_Char_To_MaixCAM(key_char);
		}

		// 原有传感器数据处理
		D = ( (Camera_RxPacket[0]<<8) & Camera_RxPacket[1] ) / 100.0f;

		printf("t0.txt=\"直径/边长D：%.1fmm\"\xff\xff\xff", D);
		printf("t1.txt=\"  距离x：%.1fmm\"\xff\xff\xff", x);
		printf("t2.txt=\"  电流Is：%.1fmA\"\xff\xff\xff", Is);
		printf("t3.txt=\"  功率P：%.1fW\"\xff\xff\xff", P);
		printf("t4.txt=\"最大功率Pm：%.1fW\"\xff\xff\xff", Pm);

		delay_ms(10);  // 10ms扫描间隔
    }
}
