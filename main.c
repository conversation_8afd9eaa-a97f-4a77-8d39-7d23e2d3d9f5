#include "delay.h"
#include "sys.h"
#include "usart.h"
#include <Camera.h>
#include "matrix_keyboard_4x4.h"
#include "maixcam_comm.h"

float D, x, Is, P, Pm, xx, xy;
uint8_t display_mode = 0;  // 显示模式: 0-传感器数据, 1-键盘测试

int main(void)
{
    delay_init(); // 延时函数初始化
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2); // 设置中断优先级分组2
    uart_init(9600); // 串口初始化为9600
	Camera_Init();

	// 初始化矩阵键盘和MaixCAM通信
	Matrix_Keyboard_Init();
	MaixCAM_UART_Init();

	// 初始化数据
	D = 2011.1;
	x = 22.22;
	Is = 33.333;
	P = 44.444;
	Pm = 55.555;

	printf("STM32系统启动完成\r\n");
	printf("按键功能:\r\n");
	printf("1-9,0: 发送数字到MaixCAM\r\n");
	printf("A-D: 发送字母到MaixCAM\r\n");
	printf("*: 切换显示模式\r\n");
	printf("#: 清除MaixCAM屏幕\r\n");

	// 发送欢迎信息到MaixCAM
	delay_ms(1000);  // 等待MaixCAM启动
	MaixCAM_ClearScreen();
	delay_ms(100);
	MaixCAM_SendText("STM32 Connected!", MAIXCAM_COLOR_GREEN);
	delay_ms(500);
	MaixCAM_SendText("Press keys to display", MAIXCAM_COLOR_WHITE);

    while (1)
	{
		// 扫描矩阵键盘
		uint8_t key = Matrix_Keyboard_GetKey();
		if(key != KEY_VALUE_NONE)
		{
			char key_char = Matrix_Keyboard_GetChar(key);
			printf("按键: %c, 发送到MaixCAM显示\r\n", key_char);

			// 处理特殊按键
			if(key == KEY_VALUE_STAR)  // '*' 键切换显示模式
			{
				display_mode = !display_mode;
				if(display_mode == 0)
				{
					MaixCAM_SendText("Mode: Sensor Data", MAIXCAM_COLOR_CYAN);
					printf("切换到传感器数据显示模式\r\n");
				}
				else
				{
					MaixCAM_SendText("Mode: Keyboard Test", MAIXCAM_COLOR_CYAN);
					printf("切换到键盘测试模式\r\n");
				}
			}
			else if(key == KEY_VALUE_HASH)  // '#' 键清屏
			{
				MaixCAM_ClearScreen();
				printf("清除MaixCAM屏幕\r\n");
			}
			else
			{
				// 发送按键字符到MaixCAM显示
				char display_text[50];
				snprintf(display_text, sizeof(display_text), "Key Pressed: %c", key_char);

				// 根据按键设置不同颜色
				uint8_t color = MAIXCAM_COLOR_WHITE;
				if(key >= KEY_VALUE_1 && key <= KEY_VALUE_9) color = MAIXCAM_COLOR_GREEN;
				else if(key == KEY_VALUE_0) color = MAIXCAM_COLOR_BLUE;
				else if(key >= KEY_VALUE_A && key <= KEY_VALUE_D) color = MAIXCAM_COLOR_RED;

				MaixCAM_SendText(display_text, color);
			}
		}

		// 根据显示模式更新数据
		if(display_mode == 0)  // 传感器数据模式
		{
			static uint32_t last_sensor_update = 0;
			static uint32_t current_time = 0;
			current_time++;  // 简化的时间计数

			if(current_time - last_sensor_update > 1000)  // 每1000次循环更新一次
			{
				D = ( (Camera_RxPacket[0]<<8) & Camera_RxPacket[1] ) / 100.0f;

				// 发送传感器数据到串口(原有功能)
				printf("t0.txt=\"直径/边长D：%.1fmm\"\xff\xff\xff", D);
				printf("t1.txt=\"  距离x：%.1fmm\"\xff\xff\xff", x);
				printf("t2.txt=\"  电流Is：%.1fmA\"\xff\xff\xff", Is);
				printf("t3.txt=\"  功率P：%.1fW\"\xff\xff\xff", P);
				printf("t4.txt=\"最大功率Pm：%.1fW\"\xff\xff\xff", Pm);

				// 同时发送到MaixCAM显示
				char sensor_text[100];
				snprintf(sensor_text, sizeof(sensor_text), "D:%.1fmm x:%.1fmm P:%.1fW", D, x, P);
				MaixCAM_SendText(sensor_text, MAIXCAM_COLOR_YELLOW);

				last_sensor_update = current_time;
			}
		}

		delay_ms(10);  // 10ms扫描间隔
    }
}
