#include "delay.h"
#include "sys.h"
#include "usart.h"
#include <Camera.h>

float D, x, Is, P, Pm, xx, xy;

int main(void)
{
    delay_init(); // ��ʱ������ʼ��
    NVIC_PriorityGroupConfig(NVIC_PriorityGroup_2); // �����ж����ȼ�����2
    uart_init(9600); // ���ڳ�ʼ��Ϊ9600
	Camera_Init();
	D = 2011.1;
	x = 22.22;
	Is = 33.333;
	P = 44.444;
	Pm = 55.555;
	
    while (1)
	{
		D = ( (Camera_RxPacket[0]<<8) & Camera_RxPacket[1] ) / 100.0f;
//		xx = ( (Camera_RxPacket[2]<<8) & Camera_RxPacket[3] ) / 100.0f;
//		xy = ( (Camera_RxPacket[4]<<8) & Camera_RxPacket[5] ) / 100.0f;
		
		printf("t0.txt=\"ֱ��/�߳�D��%.1fmm\"\xff\xff\xff", D);
		printf("t1.txt=\"  ����x��%.1fmm\"\xff\xff\xff", x);
		printf("t2.txt=\"  ����Is��%.1fmA\"\xff\xff\xff", Is);
		printf("t3.txt=\"  ����P��%.1fW\"\xff\xff\xff", P);
		printf("t4.txt=\"�����Pm��%.1fW\"\xff\xff\xff", Pm);
		
    }
}
